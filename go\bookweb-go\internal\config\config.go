package config

import (
	"github.com/spf13/viper"
)

type Config struct {
	App      AppConfig      `mapstructure:"app"`
	Database DatabaseConfig `mapstructure:"database"`
	Redis    RedisConfig    `mapstructure:"redis"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	AI       AIConfig       `mapstructure:"ai"`
	Log      LogConfig      `mapstructure:"log"`
}

type AppConfig struct {
	Name         string `mapstructure:"name"`
	Mode         string `mapstructure:"mode"`
	Port         string `mapstructure:"port"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	IdleTimeout  int    `mapstructure:"idle_timeout"`
}

type DatabaseConfig struct {
	MySQL MySQLConfig `mapstructure:"mysql"`
	Redis RedisConfig `mapstructure:"redis"`
}

type MySQLConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
	Database     string `mapstructure:"database"`
	MaxOpenConns int    `mapstructure:"max_open_conns"`
	MaxIdleConns int    `mapstructure:"max_idle_conns"`
	MaxLifetime  int    `mapstructure:"max_lifetime"`
}

type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	PoolSize int    `mapstructure:"pool_size"`
}

type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	ExpireTime int    `mapstructure:"expire_time"`
}

type AIConfig struct {
	Endpoints []AIEndpoint `mapstructure:"endpoints"`
	Timeout   int          `mapstructure:"timeout"`
	MaxRetry  int          `mapstructure:"max_retry"`
}

type AIEndpoint struct {
	Name     string `mapstructure:"name"`
	Provider string `mapstructure:"provider"` // openai, anthropic, etc.
	URL      string `mapstructure:"url"`
	APIKey   string `mapstructure:"api_key"`
	Weight   int    `mapstructure:"weight"`
	Model    string `mapstructure:"model"` // 模型名称
}

type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 设置默认值
	setDefaults()

	// 读取环境变量
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

func setDefaults() {
	viper.SetDefault("app.name", "BookWeb")
	viper.SetDefault("app.mode", "development")
	viper.SetDefault("app.port", "8080")
	viper.SetDefault("app.read_timeout", 60)
	viper.SetDefault("app.write_timeout", 60)
	viper.SetDefault("app.idle_timeout", 120)

	viper.SetDefault("database.mysql.max_open_conns", 100)
	viper.SetDefault("database.mysql.max_idle_conns", 10)
	viper.SetDefault("database.mysql.max_lifetime", 3600)

	viper.SetDefault("redis.pool_size", 100)

	viper.SetDefault("jwt.expire_time", 3600)

	viper.SetDefault("ai.timeout", 120)
	viper.SetDefault("ai.max_retry", 3)

	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
}
