# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
bookweb
bookweb_unix
migrate
*.log
coverage.out

# Config files with secrets
configs/config.local.yaml
configs/config.prod.yaml
.env
.env.local

# Docker volumes
mysql_data/
redis_data/

# Temporary files
tmp/
temp/
