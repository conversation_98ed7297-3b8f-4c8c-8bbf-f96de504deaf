package service

import (
	"bookweb/internal/dto/request"
	"bookweb/internal/model"
	"bookweb/internal/repository"
)

type ChapterService struct {
	chapterRepo *repository.ChapterRepository
	userRepo    *repository.UserRepository
}

func NewChapterService(chapterRepo *repository.ChapterRepository, userRepo *repository.UserRepository) *ChapterService {
	return &ChapterService{
		chapterRepo: chapterRepo,
		userRepo:    userRepo,
	}
}

func (s *ChapterService) CreateChapter(userID uint, req *request.CreateChapterRequest) (*model.Chapter, error) {
	// TODO: 实现章节创建逻辑
	chapter := &model.Chapter{
		NovelID:       req.NovelID,
		Title:         req.Title,
		ChapterNumber: 1, // TODO: 计算章节号
		Status:        0, // 生成中
	}

	return s.chapterRepo.Create(chapter)
}

func (s *ChapterService) GetChapterByID(id uint) (*model.Chapter, error) {
	return s.chapterRepo.GetByID(id)
}

func (s *ChapterService) GetChaptersByNovelID(novelID, userID uint, page, size int) ([]model.Chapter, int, error) {
	// TODO: 验证用户权限
	return s.chapterRepo.GetByNovelID(novelID, page, size)
}

func (s *ChapterService) UpdateChapter(id uint, data map[string]interface{}) error {
	return s.chapterRepo.Update(id, data)
}

func (s *ChapterService) UpdateStatus(id uint, status int) error {
	return s.chapterRepo.UpdateStatus(id, status)
}

func (s *ChapterService) UpdateStatusWithError(id uint, status int, errorMsg string) error {
	// TODO: 实现错误状态更新
	return s.chapterRepo.UpdateStatus(id, status)
}

func (s *ChapterService) CheckUserQuota(userID uint) bool {
	// TODO: 实现配额检查
	return true
}

func (s *ChapterService) UpdateUserQuota(userID uint) error {
	// TODO: 实现配额更新
	return nil
}
