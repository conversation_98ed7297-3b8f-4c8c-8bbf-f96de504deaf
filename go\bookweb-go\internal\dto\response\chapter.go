package response

import "time"

type ChapterResponse struct {
	ID             uint      `json:"id"`
	NovelID        uint      `json:"novel_id"`
	ChapterNumber  int       `json:"chapter_number"`
	Title          string    `json:"title"`
	Content        string    `json:"content,omitempty"`
	WordCount      int       `json:"word_count"`
	Status         int       `json:"status"`
	GenerationTime int       `json:"generation_time"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type CreateChapterResponse struct {
	ChapterID uint   `json:"chapter_id"`
	TaskID    string `json:"task_id"`
	Status    string `json:"status"`
	Message   string `json:"message"`
}
