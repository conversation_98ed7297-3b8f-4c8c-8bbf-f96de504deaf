# AI小说生成Web应用核心代码实现示例

## 1. 项目依赖配置

### requirements.txt
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
pymysql==1.1.0
redis==5.0.1
celery==5.3.4
pydantic==2.5.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
alembic==1.13.0
pytest==7.4.3
pytest-asyncio==0.21.1
python-dotenv==1.0.0
```

### pyproject.toml
```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-novel-generator"
version = "1.0.0"
description = "AI小说生成Web应用"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "MIT"}
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.23",
    "pymysql>=1.1.0",
    "redis>=5.0.1",
    "celery>=5.3.4",
    "pydantic>=2.5.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
```

## 2. 应用配置

### app/config.py
```python
from pydantic import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # 基础配置
    PROJECT_NAME: str = "AI Novel Generator"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: str = "mysql+pymysql://user:password@localhost:3306/bookweb"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    DATABASE_ECHO: bool = False
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: Optional[str] = None
    REDIS_MAX_CONNECTIONS: int = 100
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # AI服务配置
    AI_SERVICE_URL: str = "https://api.openai.com/v1"
    AI_SERVICE_API_KEY: str = "your-openai-api-key"
    AI_MODEL_NAME: str = "gpt-3.5-turbo"
    
    # 限流配置
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_PER_HOUR: int = 1000
    RATE_LIMIT_PER_DAY: int = 10000
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/2"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: set = {".jpg", ".jpeg", ".png", ".gif"}
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

## 3. 数据库模型

### app/models/base.py
```python
from sqlalchemy import Column, Integer, DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session
from typing import Any

Base = declarative_base()

class BaseModel(Base):
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "BaseModel":
        """从字典创建实例"""
        return cls(**{
            key: value for key, value in data.items()
            if hasattr(cls, key)
        })
```

### app/models/user.py
```python
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer
from sqlalchemy.orm import relationship
from .base import BaseModel

class User(BaseModel):
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    phone = Column(String(20), unique=True, nullable=True)
    password_hash = Column(String(255), nullable=False)
    salt = Column(String(32), nullable=False)
    nickname = Column(String(100), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    bio = Column(Text, nullable=True)
    status = Column(Integer, default=1, nullable=False)  # 0-禁用 1-正常
    email_verified = Column(Boolean, default=False)
    phone_verified = Column(Boolean, default=False)
    last_login_at = Column(DateTime, nullable=True)
    last_login_ip = Column(String(45), nullable=True)
    deleted_at = Column(DateTime, nullable=True)
    
    # 关联关系
    novels = relationship("Novel", back_populates="author", cascade="all, delete-orphan")
    generation_tasks = relationship("GenerationTask", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"
```

### app/models/novel.py
```python
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from .base import BaseModel

class Novel(BaseModel):
    __tablename__ = "novels"
    
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(200), nullable=False)
    subtitle = Column(String(200), nullable=True)
    description = Column(Text, nullable=True)
    cover_url = Column(String(255), nullable=True)
    genre = Column(String(50), nullable=True)
    sub_genre = Column(String(50), nullable=True)
    tags = Column(JSON, nullable=True)
    language = Column(String(10), default="zh-CN")
    writing_style = Column(String(50), nullable=True)
    status = Column(Integer, default=0, nullable=False)  # 0-草稿 1-连载中 2-完结
    word_count = Column(Integer, default=0)
    chapter_count = Column(Integer, default=0)
    view_count = Column(Integer, default=0)
    like_count = Column(Integer, default=0)
    is_public = Column(Boolean, default=True)
    published_at = Column(DateTime, nullable=True)
    deleted_at = Column(DateTime, nullable=True)
    
    # 关联关系
    author = relationship("User", back_populates="novels")
    chapters = relationship("NovelChapter", back_populates="novel", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Novel(id={self.id}, title='{self.title}')>"

class NovelChapter(BaseModel):
    __tablename__ = "novel_chapters"
    
    novel_id = Column(Integer, ForeignKey("novels.id"), nullable=False)
    chapter_number = Column(Integer, nullable=False)
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    summary = Column(Text, nullable=True)
    word_count = Column(Integer, default=0)
    status = Column(Integer, default=1, nullable=False)  # 0-草稿 1-已发布
    view_count = Column(Integer, default=0)
    published_at = Column(DateTime, nullable=True)
    deleted_at = Column(DateTime, nullable=True)
    
    # 关联关系
    novel = relationship("Novel", back_populates="chapters")
    
    def __repr__(self):
        return f"<NovelChapter(id={self.id}, title='{self.title}')>"
```

## 4. 数据访问层

### app/repositories/base.py
```python
from typing import TypeVar, Generic, Type, Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from app.models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)

class BaseRepository(Generic[ModelType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model
    
    def get(self, db: Session, id: int) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        return db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(
        self, 
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """获取多个记录"""
        query = db.query(self.model)
        
        # 应用过滤条件
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key):
                    query = query.filter(getattr(self.model, key) == value)
        
        # 应用排序
        if order_by and hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        
        return query.offset(skip).limit(limit).all()
    
    def create(self, db: Session, obj_in: Dict[str, Any]) -> ModelType:
        """创建新记录"""
        db_obj = self.model(**obj_in)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update(
        self, 
        db: Session, 
        db_obj: ModelType, 
        obj_in: Dict[str, Any]
    ) -> ModelType:
        """更新记录"""
        for field, value in obj_in.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def delete(self, db: Session, id: int) -> Optional[ModelType]:
        """删除记录"""
        obj = self.get(db, id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj
    
    def count(self, db: Session, filters: Optional[Dict[str, Any]] = None) -> int:
        """统计记录数量"""
        query = db.query(self.model)
        
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key):
                    query = query.filter(getattr(self.model, key) == value)
        
        return query.count()
```

### app/repositories/user_repo.py
```python
from typing import Optional
from sqlalchemy.orm import Session
from app.models.user import User
from .base import BaseRepository

class UserRepository(BaseRepository[User]):
    def __init__(self):
        super().__init__(User)
    
    def get_by_username(self, db: Session, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return db.query(User).filter(User.username == username).first()
    
    def get_by_email(self, db: Session, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return db.query(User).filter(User.email == email).first()
    
    def get_active_users(self, db: Session, skip: int = 0, limit: int = 100):
        """获取活跃用户列表"""
        return db.query(User).filter(
            User.status == 1,
            User.deleted_at.is_(None)
        ).offset(skip).limit(limit).all()
    
    def update_last_login(self, db: Session, user_id: int, ip_address: str):
        """更新最后登录信息"""
        from datetime import datetime
        user = self.get(db, user_id)
        if user:
            user.last_login_at = datetime.utcnow()
            user.last_login_ip = ip_address
            db.commit()
            db.refresh(user)
        return user

user_repo = UserRepository()
```

## 5. 业务服务层

### app/services/auth_service.py
```python
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.orm import Session
from app.config import settings
from app.repositories.user_repo import user_repo
from app.models.user import User
import secrets

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AuthService:
    def __init__(self):
        self.pwd_context = pwd_context
    
    def verify_password(self, plain_password: str, hashed_password: str, salt: str) -> bool:
        """验证密码"""
        return self.pwd_context.verify(plain_password + salt, hashed_password)
    
    def get_password_hash(self, password: str, salt: str) -> str:
        """生成密码哈希"""
        return self.pwd_context.hash(password + salt)
    
    def generate_salt(self) -> str:
        """生成密码盐值"""
        return secrets.token_hex(16)
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None):
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[dict]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            return payload
        except JWTError:
            return None
    
    def authenticate_user(self, db: Session, username: str, password: str) -> Optional[User]:
        """用户认证"""
        user = user_repo.get_by_username(db, username)
        if not user:
            user = user_repo.get_by_email(db, username)
        
        if not user or not self.verify_password(password, user.password_hash, user.salt):
            return None
        
        return user
    
    def register_user(self, db: Session, user_data: dict) -> User:
        """用户注册"""
        salt = self.generate_salt()
        password_hash = self.get_password_hash(user_data["password"], salt)
        
        user_create_data = {
            "username": user_data["username"],
            "email": user_data["email"],
            "password_hash": password_hash,
            "salt": salt,
            "nickname": user_data.get("nickname"),
            "phone": user_data.get("phone")
        }
        
        return user_repo.create(db, user_create_data)

auth_service = AuthService()
```

## 6. API路由层

### app/api/v1/auth.py
```python
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.auth_service import auth_service
from app.schemas.auth import UserRegister, UserLogin, Token
from app.schemas.response import ResponseModel
from app.config import settings

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

@router.post("/register", response_model=ResponseModel)
async def register(user_data: UserRegister, db: Session = Depends(get_db)):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        existing_user = user_repo.get_by_username(db, user_data.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 检查邮箱是否已存在
        existing_email = user_repo.get_by_email(db, user_data.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
        
        # 创建用户
        user = auth_service.register_user(db, user_data.dict())
        
        return ResponseModel(
            code=201,
            message="注册成功",
            data={
                "user_id": user.id,
                "username": user.username,
                "email": user.email
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败"
        )

@router.post("/login", response_model=ResponseModel[Token])
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """用户登录"""
    user = auth_service.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = auth_service.create_access_token(
        data={"sub": str(user.id), "username": user.username},
        expires_delta=access_token_expires
    )
    
    return ResponseModel(
        data=Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
    )
```

这个核心代码示例展示了FastAPI应用的基础架构，包括配置管理、数据模型、数据访问层、业务服务层和API路由层的实现。代码遵循了良好的分层架构原则，具有高可维护性和扩展性。
