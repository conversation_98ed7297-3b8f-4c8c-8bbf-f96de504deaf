# AI小说生成Web应用数据库设计

## 1. 数据库设计原则

### 1.1 设计原则
- **规范化设计**: 遵循第三范式，减少数据冗余
- **性能优化**: 合理设计索引，优化查询性能
- **扩展性**: 预留扩展字段，支持业务发展
- **数据完整性**: 使用外键约束保证数据一致性
- **软删除**: 重要数据使用软删除机制

### 1.2 命名规范
- 表名：小写字母+下划线，复数形式
- 字段名：小写字母+下划线
- 索引名：idx_表名_字段名
- 外键名：fk_表名_字段名

## 2. 核心表结构设计

### 2.1 用户管理相关表

#### 用户基础信息表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    nickname VARCHAR(100) COMMENT '昵称',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别: 0-未知 1-男 2-女',
    birthday DATE COMMENT '生日',
    bio TEXT COMMENT '个人简介',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用 1-正常 2-待验证',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
    phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机是否验证',
    last_login_at TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';
```

#### 用户角色表 (user_roles)
```sql
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '角色名称',
    description VARCHAR(200) COMMENT '角色描述',
    permissions JSON COMMENT '权限列表',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认角色',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用 1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_name (name),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色表';
```

#### 用户角色关联表 (user_role_relations)
```sql
CREATE TABLE user_role_relations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    assigned_by BIGINT COMMENT '分配者ID',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    expires_at TIMESTAMP COMMENT '过期时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
```

#### 用户配额表 (user_quotas)
```sql
CREATE TABLE user_quotas (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配额ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    quota_type VARCHAR(20) NOT NULL COMMENT '配额类型: daily, monthly, total',
    resource_type VARCHAR(20) NOT NULL COMMENT '资源类型: generation, storage, api_calls',
    total_quota INT NOT NULL DEFAULT 0 COMMENT '总配额',
    used_quota INT NOT NULL DEFAULT 0 COMMENT '已使用配额',
    reset_date DATE COMMENT '重置日期',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用 1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_quota (user_id, quota_type, resource_type),
    INDEX idx_user_id (user_id),
    INDEX idx_quota_type (quota_type),
    INDEX idx_reset_date (reset_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配额表';
```

### 2.2 小说内容相关表

#### 小说信息表 (novels)
```sql
CREATE TABLE novels (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '小说ID',
    user_id BIGINT NOT NULL COMMENT '作者用户ID',
    title VARCHAR(200) NOT NULL COMMENT '小说标题',
    subtitle VARCHAR(200) COMMENT '副标题',
    description TEXT COMMENT '小说简介',
    cover_url VARCHAR(255) COMMENT '封面图片URL',
    genre VARCHAR(50) COMMENT '题材类型',
    sub_genre VARCHAR(50) COMMENT '子题材',
    tags JSON COMMENT '标签列表',
    language VARCHAR(10) DEFAULT 'zh-CN' COMMENT '语言',
    writing_style VARCHAR(50) COMMENT '写作风格',
    target_audience VARCHAR(20) COMMENT '目标读者群体',
    content_rating VARCHAR(10) DEFAULT 'G' COMMENT '内容分级',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-草稿 1-连载中 2-完结 3-暂停 4-删除',
    word_count INT DEFAULT 0 COMMENT '总字数',
    chapter_count INT DEFAULT 0 COMMENT '章节数',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    collect_count INT DEFAULT 0 COMMENT '收藏数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    is_original BOOLEAN DEFAULT TRUE COMMENT '是否原创',
    copyright_info JSON COMMENT '版权信息',
    published_at TIMESTAMP COMMENT '发布时间',
    completed_at TIMESTAMP COMMENT '完结时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_genre (genre),
    INDEX idx_status (status),
    INDEX idx_is_public (is_public),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at),
    INDEX idx_word_count (word_count),
    INDEX idx_view_count (view_count),
    FULLTEXT idx_title_desc (title, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小说信息表';
```

#### 小说章节表 (novel_chapters)
```sql
CREATE TABLE novel_chapters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '章节ID',
    novel_id BIGINT NOT NULL COMMENT '小说ID',
    chapter_number INT NOT NULL COMMENT '章节序号',
    title VARCHAR(200) NOT NULL COMMENT '章节标题',
    content LONGTEXT NOT NULL COMMENT '章节内容',
    summary TEXT COMMENT '章节摘要',
    word_count INT DEFAULT 0 COMMENT '字数',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-草稿 1-已发布 2-删除',
    is_free BOOLEAN DEFAULT TRUE COMMENT '是否免费',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '章节价格',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    published_at TIMESTAMP COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
    UNIQUE KEY uk_novel_chapter (novel_id, chapter_number),
    INDEX idx_novel_id (novel_id),
    INDEX idx_chapter_number (chapter_number),
    INDEX idx_status (status),
    INDEX idx_published_at (published_at),
    FULLTEXT idx_title_content (title, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小说章节表';
```

### 2.3 AI生成相关表

#### 生成任务表 (generation_tasks)
```sql
CREATE TABLE generation_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    novel_id BIGINT COMMENT '关联小说ID',
    chapter_id BIGINT COMMENT '关联章节ID',
    task_type VARCHAR(20) NOT NULL COMMENT '任务类型: novel, chapter, continuation, rewrite',
    task_name VARCHAR(100) COMMENT '任务名称',
    prompt TEXT NOT NULL COMMENT '生成提示词',
    parameters JSON COMMENT '生成参数',
    model_name VARCHAR(50) COMMENT '使用的AI模型',
    model_version VARCHAR(20) COMMENT '模型版本',
    priority TINYINT DEFAULT 5 COMMENT '优先级: 1-10',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态: pending, processing, completed, failed, cancelled',
    progress INT DEFAULT 0 COMMENT '进度: 0-100',
    estimated_duration INT COMMENT '预估耗时(秒)',
    actual_duration INT COMMENT '实际耗时(秒)',
    result_data JSON COMMENT '生成结果数据',
    result_text LONGTEXT COMMENT '生成的文本内容',
    result_metadata JSON COMMENT '结果元数据',
    error_code VARCHAR(20) COMMENT '错误代码',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retries INT DEFAULT 3 COMMENT '最大重试次数',
    started_at TIMESTAMP COMMENT '开始时间',
    completed_at TIMESTAMP COMMENT '完成时间',
    expires_at TIMESTAMP COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES novel_chapters(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_novel_id (novel_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    INDEX idx_started_at (started_at),
    INDEX idx_completed_at (completed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生成任务表';
```

#### 生成模板表 (generation_templates)
```sql
CREATE TABLE generation_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    description TEXT COMMENT '模板描述',
    category VARCHAR(50) COMMENT '模板分类',
    template_type VARCHAR(20) NOT NULL COMMENT '模板类型: novel, chapter, character',
    prompt_template TEXT NOT NULL COMMENT '提示词模板',
    default_parameters JSON COMMENT '默认参数',
    tags JSON COMMENT '标签',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    is_official BOOLEAN DEFAULT FALSE COMMENT '是否官方模板',
    created_by BIGINT COMMENT '创建者ID',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用 1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_template_type (template_type),
    INDEX idx_is_public (is_public),
    INDEX idx_usage_count (usage_count),
    INDEX idx_rating (rating),
    FULLTEXT idx_name_desc (name, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生成模板表';
```

### 2.4 系统管理相关表

#### 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型: string, int, float, bool, json',
    category VARCHAR(50) COMMENT '配置分类',
    description TEXT COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开配置',
    is_editable BOOLEAN DEFAULT TRUE COMMENT '是否可编辑',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

#### 操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT COMMENT '操作用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id BIGINT COMMENT '资源ID',
    operation_desc VARCHAR(200) COMMENT '操作描述',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_url VARCHAR(255) COMMENT '请求URL',
    request_params JSON COMMENT '请求参数',
    response_code INT COMMENT '响应状态码',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    execution_time INT COMMENT '执行时间(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_resource_type (resource_type),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

## 3. 索引优化策略

### 3.1 主要索引设计
- **主键索引**: 所有表都使用自增BIGINT主键
- **唯一索引**: 用户名、邮箱等唯一字段
- **复合索引**: 常用查询条件组合
- **全文索引**: 标题、内容等文本搜索字段

### 3.2 查询优化
- 避免SELECT *，只查询需要的字段
- 使用LIMIT限制结果集大小
- 合理使用JOIN，避免N+1查询
- 使用EXPLAIN分析查询计划

## 4. 数据库维护

### 4.1 备份策略
- 每日全量备份
- 实时增量备份
- 定期备份验证
- 异地备份存储

### 4.2 监控指标
- 连接数监控
- 慢查询监控
- 锁等待监控
- 存储空间监控

这个数据库设计提供了完整的数据存储方案，支持用户管理、内容管理、AI生成等核心功能，同时考虑了性能优化和扩展性需求。
