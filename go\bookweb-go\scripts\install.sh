#!/bin/bash

echo "Installing BookWeb Go dependencies..."

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "Error: Go is not installed or not in PATH"
    echo "Please install Go from https://golang.org/dl/"
    exit 1
fi

echo "Go is installed, proceeding with dependency installation..."

# Initialize Go module if not exists
if [ ! -f go.mod ]; then
    echo "Initializing Go module..."
    go mod init bookweb
fi

# Download dependencies
echo "Downloading dependencies..."
go mod download
go mod tidy

echo "Dependencies installed successfully!"
echo ""
echo "To run the application:"
echo "  1. Configure configs/config.yaml with your database and AI API settings"
echo "  2. Run: make run"
echo "  or: go run cmd/api/main.go"
