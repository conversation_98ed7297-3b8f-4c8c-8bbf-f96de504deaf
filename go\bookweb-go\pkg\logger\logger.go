package logger

import (
	"os"

	"github.com/sirupsen/logrus"
)

var log *logrus.Logger

func Init(level string) {
	log = logrus.New()

	// 设置日志级别
	switch level {
	case "debug":
		log.SetLevel(logrus.DebugLevel)
	case "info":
		log.SetLevel(logrus.InfoLevel)
	case "warn":
		log.SetLevel(logrus.WarnLevel)
	case "error":
		log.SetLevel(logrus.ErrorLevel)
	default:
		log.SetLevel(logrus.InfoLevel)
	}

	// 设置JSON格式
	log.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})

	log.SetOutput(os.Stdout)
}

func Debug(msg string, fields ...interface{}) {
	log.WithFields(parseFields(fields...)).Debug(msg)
}

func Info(msg string, fields ...interface{}) {
	log.WithFields(parseFields(fields...)).Info(msg)
}

func Warn(msg string, fields ...interface{}) {
	log.WithFields(parseFields(fields...)).Warn(msg)
}

func Error(msg string, fields ...interface{}) {
	log.WithFields(parseFields(fields...)).Error(msg)
}

func Fatal(msg string, fields ...interface{}) {
	log.WithFields(parseFields(fields...)).Fatal(msg)
}

func parseFields(fields ...interface{}) logrus.Fields {
	result := make(logrus.Fields)
	for i := 0; i < len(fields); i += 2 {
		if i+1 < len(fields) {
			key := fields[i].(string)
			value := fields[i+1]
			result[key] = value
		}
	}
	return result
}
