# BookWeb Go 项目结构说明

## 📁 目录结构

```
bookweb-go/
├── cmd/                           # 应用程序入口点
│   ├── api/                      # API服务器
│   │   └── main.go              # 主入口文件
│   ├── worker/                   # 异步任务处理器
│   └── migrate/                  # 数据库迁移工具
│
├── internal/                      # 内部应用代码
│   ├── api/                      # API层
│   │   ├── handlers/             # HTTP处理器
│   │   ├── middleware/           # 中间件
│   │   └── routes/               # 路由定义
│   ├── service/                  # 业务逻辑层
│   ├── repository/               # 数据访问层
│   ├── model/                    # 数据模型
│   ├── dto/                      # 数据传输对象
│   │   ├── request/              # 请求DTO
│   │   └── response/             # 响应DTO
│   ├── config/                   # 配置管理
│   ├── database/                 # 数据库连接
│   ├── cache/                    # 缓存管理
│   ├── queue/                    # 任务队列
│   ├── utils/                    # 工具函数
│   └── worker/                   # 异步任务处理
│
├── pkg/                          # 可重用的公共包
│   ├── logger/                   # 日志包
│   ├── errors/                   # 错误处理
│   ├── response/                 # 统一响应
│   └── metrics/                  # 指标监控
│
├── configs/                      # 配置文件
│   └── config.yaml              # 主配置文件
│
├── migrations/                   # 数据库迁移文件
├── scripts/                      # 脚本文件
│   ├── install.bat              # Windows安装脚本
│   ├── install.sh               # Linux/Mac安装脚本
│   └── start.bat                # Windows启动脚本
│
├── docker/                       # Docker相关文件
├── docs/                         # 文档
├── go.mod                        # Go模块定义
├── go.sum                        # 依赖版本锁定
├── Makefile                      # 构建脚本
├── Dockerfile                    # Docker镜像构建
├── docker-compose.yml            # Docker编排
└── README.md                     # 项目说明
```

## 🏗️ 架构设计

### 分层架构
1. **API层** (`internal/api/`): 处理HTTP请求和响应
2. **服务层** (`internal/service/`): 业务逻辑处理
3. **仓储层** (`internal/repository/`): 数据访问抽象
4. **模型层** (`internal/model/`): 数据结构定义

### 核心组件

#### 1. 配置管理 (`internal/config/`)
- 使用Viper进行配置管理
- 支持YAML配置文件
- 环境变量覆盖
- 默认值设置

#### 2. 数据库 (`internal/database/`)
- MySQL主数据库（GORM）
- Redis缓存数据库
- 连接池管理
- 自动迁移

#### 3. 中间件 (`internal/api/middleware/`)
- CORS跨域处理
- 错误恢复
- 认证授权
- 限流控制

#### 4. 日志系统 (`pkg/logger/`)
- 结构化日志（Logrus）
- 多级别日志
- JSON格式输出

#### 5. 监控指标 (`pkg/metrics/`)
- Prometheus指标收集
- HTTP请求监控
- AI生成任务监控
- 数据库连接监控

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装Go 1.21+
# 安装MySQL 8.0+
# 安装Redis 7.0+
```

### 2. 项目初始化
```bash
# Windows
scripts\install.bat

# Linux/Mac
chmod +x scripts/install.sh
./scripts/install.sh
```

### 3. 配置设置
编辑 `configs/config.yaml` 文件，设置：
- 数据库连接信息
- Redis连接信息
- AI API密钥
- JWT密钥

### 4. 运行应用
```bash
# 使用Makefile
make run

# 或直接运行
go run cmd/api/main.go

# Windows脚本
scripts\start.bat
```

## 📝 开发指南

### 添加新的API端点
1. 在 `internal/model/` 中定义数据模型
2. 在 `internal/dto/` 中定义请求/响应DTO
3. 在 `internal/repository/` 中实现数据访问
4. 在 `internal/service/` 中实现业务逻辑
5. 在 `internal/api/handlers/` 中实现HTTP处理器
6. 在 `internal/api/routes/` 中注册路由

### 代码规范
- 使用Go标准格式化工具：`make fmt`
- 使用golangci-lint进行代码检查：`make lint`
- 编写单元测试：`make test`
- 生成测试覆盖率报告：`make test-coverage`

### 部署
```bash
# Docker部署
make docker-build
make docker-run

# 生产构建
make build-linux
```

## 🔧 配置说明

### 应用配置
- `app.name`: 应用名称
- `app.mode`: 运行模式（development/production）
- `app.port`: 监听端口

### 数据库配置
- `database.mysql.*`: MySQL连接配置
- `redis.*`: Redis连接配置

### AI配置
- `ai.endpoints`: AI提供商配置
- `ai.timeout`: 请求超时时间
- `ai.max_retry`: 最大重试次数

### 日志配置
- `log.level`: 日志级别
- `log.format`: 日志格式

## 📊 监控和运维

### 健康检查
- `GET /health`: 应用健康状态

### 指标监控
- `GET /metrics`: Prometheus指标

### 日志查看
- 应用日志输出到标准输出
- JSON格式便于日志收集系统处理

这个项目结构提供了一个完整的、可扩展的Go Web应用框架，适合构建高性能的AI小说生成系统。
