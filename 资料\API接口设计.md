# AI小说生成Web应用API接口设计

## 1. API设计原则

### 1.1 RESTful设计规范
- 使用HTTP动词表示操作：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- 使用名词表示资源，复数形式
- 统一的URL结构：`/api/v1/resource/{id}/sub-resource`
- 使用HTTP状态码表示结果状态
- 统一的响应格式

### 1.2 版本管理
- URL版本控制：`/api/v1/`, `/api/v2/`
- 向后兼容原则
- 废弃接口渐进式下线

### 1.3 安全设计
- JWT Token认证
- API密钥验证
- 请求签名验证
- 参数验证和过滤
- 敏感信息脱敏

## 2. 通用响应格式

### 2.1 标准响应结构
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2024-01-01T12:00:00Z",
    "request_id": "req_123456789"
}
```

### 2.2 分页响应结构
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 100,
            "pages": 5
        }
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2.3 错误响应结构
```json
{
    "code": 400,
    "message": "参数验证失败",
    "error": {
        "type": "ValidationError",
        "details": [
            {
                "field": "email",
                "message": "邮箱格式不正确"
            }
        ]
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "request_id": "req_123456789"
}
```

## 3. 认证授权接口

### 3.1 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
    "username": "john_doe",
    "email": "<EMAIL>",
    "password": "password123",
    "nickname": "John",
    "phone": "13800138000"
}

Response:
{
    "code": 201,
    "message": "注册成功",
    "data": {
        "user_id": 12345,
        "username": "john_doe",
        "email": "<EMAIL>",
        "status": "pending_verification"
    }
}
```

### 3.2 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "john_doe",
    "password": "password123",
    "remember_me": true
}

Response:
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIs...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
        "token_type": "Bearer",
        "expires_in": 1800,
        "user_info": {
            "user_id": 12345,
            "username": "john_doe",
            "nickname": "John",
            "avatar_url": "https://example.com/avatar.jpg"
        }
    }
}
```

### 3.3 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer {refresh_token}

Response:
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIs...",
        "expires_in": 1800
    }
}
```

### 3.4 用户注销
```http
POST /api/v1/auth/logout
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "注销成功"
}
```

## 4. 用户管理接口

### 4.1 获取用户信息
```http
GET /api/v1/users/profile
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "user_id": 12345,
        "username": "john_doe",
        "email": "<EMAIL>",
        "nickname": "John",
        "avatar_url": "https://example.com/avatar.jpg",
        "bio": "AI小说爱好者",
        "created_at": "2024-01-01T00:00:00Z",
        "quota_info": {
            "daily_generation": {
                "total": 10,
                "used": 3,
                "remaining": 7
            }
        }
    }
}
```

### 4.2 更新用户信息
```http
PUT /api/v1/users/profile
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "nickname": "John Doe",
    "bio": "热爱AI创作的小说家",
    "avatar_url": "https://example.com/new_avatar.jpg"
}

Response:
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "user_id": 12345,
        "nickname": "John Doe",
        "bio": "热爱AI创作的小说家",
        "updated_at": "2024-01-01T12:00:00Z"
    }
}
```

### 4.3 获取用户统计
```http
GET /api/v1/users/statistics
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "total_novels": 15,
        "total_chapters": 120,
        "total_words": 500000,
        "generation_tasks": {
            "completed": 45,
            "failed": 2,
            "pending": 1
        },
        "monthly_stats": {
            "novels_created": 3,
            "words_generated": 50000,
            "api_calls": 150
        }
    }
}
```

## 5. 小说管理接口

### 5.1 获取小说列表
```http
GET /api/v1/novels?page=1&size=20&genre=科幻&status=1&sort=created_at&order=desc
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [
            {
                "novel_id": 67890,
                "title": "AI时代的冒险",
                "description": "一个关于AI的科幻故事...",
                "genre": "科幻",
                "status": 1,
                "word_count": 50000,
                "chapter_count": 20,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T12:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 15,
            "pages": 1
        }
    }
}
```

### 5.2 创建小说
```http
POST /api/v1/novels
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "title": "新的AI小说",
    "description": "这是一个关于AI的故事",
    "genre": "科幻",
    "writing_style": "现代",
    "tags": ["AI", "科技", "未来"],
    "is_public": true
}

Response:
{
    "code": 201,
    "message": "小说创建成功",
    "data": {
        "novel_id": 67891,
        "title": "新的AI小说",
        "status": 0,
        "created_at": "2024-01-01T12:00:00Z"
    }
}
```

### 5.3 获取小说详情
```http
GET /api/v1/novels/67890
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "novel_id": 67890,
        "title": "AI时代的冒险",
        "description": "一个关于AI的科幻故事...",
        "genre": "科幻",
        "sub_genre": "硬科幻",
        "tags": ["AI", "科技", "未来"],
        "writing_style": "现代",
        "status": 1,
        "word_count": 50000,
        "chapter_count": 20,
        "view_count": 1500,
        "like_count": 89,
        "author_info": {
            "user_id": 12345,
            "username": "john_doe",
            "nickname": "John"
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
    }
}
```

### 5.4 更新小说信息
```http
PUT /api/v1/novels/67890
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "title": "AI时代的大冒险",
    "description": "更新后的故事描述",
    "tags": ["AI", "科技", "未来", "冒险"]
}

Response:
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "novel_id": 67890,
        "title": "AI时代的大冒险",
        "updated_at": "2024-01-01T12:30:00Z"
    }
}
```

### 5.5 删除小说
```http
DELETE /api/v1/novels/67890
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "删除成功"
}
```

## 6. 章节管理接口

### 6.1 获取章节列表
```http
GET /api/v1/novels/67890/chapters?page=1&size=20
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [
            {
                "chapter_id": 11111,
                "chapter_number": 1,
                "title": "第一章 开始",
                "word_count": 2500,
                "status": 1,
                "created_at": "2024-01-01T00:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 20,
            "pages": 1
        }
    }
}
```

### 6.2 获取章节内容
```http
GET /api/v1/novels/67890/chapters/11111
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "chapter_id": 11111,
        "novel_id": 67890,
        "chapter_number": 1,
        "title": "第一章 开始",
        "content": "这是第一章的内容...",
        "word_count": 2500,
        "status": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
    }
}
```

### 6.3 创建章节
```http
POST /api/v1/novels/67890/chapters
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "title": "第二章 发现",
    "content": "这是第二章的内容...",
    "chapter_number": 2
}

Response:
{
    "code": 201,
    "message": "章节创建成功",
    "data": {
        "chapter_id": 11112,
        "title": "第二章 发现",
        "chapter_number": 2,
        "word_count": 2800,
        "created_at": "2024-01-01T12:00:00Z"
    }
}
```

## 7. AI生成接口

### 7.1 创建生成任务
```http
POST /api/v1/generate/tasks
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "task_type": "novel",
    "task_name": "生成科幻小说",
    "prompt": "写一个关于AI觉醒的科幻故事",
    "parameters": {
        "genre": "科幻",
        "style": "现代",
        "length": "medium",
        "characters": ["AI机器人", "科学家"],
        "setting": "未来世界"
    },
    "model_name": "gpt-4",
    "priority": 5
}

Response:
{
    "code": 201,
    "message": "生成任务创建成功",
    "data": {
        "task_id": "task_123456",
        "status": "pending",
        "estimated_duration": 300,
        "queue_position": 3,
        "created_at": "2024-01-01T12:00:00Z"
    }
}
```

### 7.2 获取任务状态
```http
GET /api/v1/generate/tasks/task_123456
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "task_id": "task_123456",
        "task_type": "novel",
        "status": "processing",
        "progress": 65,
        "started_at": "2024-01-01T12:05:00Z",
        "estimated_completion": "2024-01-01T12:10:00Z",
        "current_step": "生成故事大纲"
    }
}
```

### 7.3 获取生成结果
```http
GET /api/v1/generate/tasks/task_123456/result
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "task_id": "task_123456",
        "status": "completed",
        "result": {
            "title": "AI觉醒纪元",
            "content": "在2045年的某个夜晚...",
            "word_count": 5000,
            "chapters": [
                {
                    "title": "第一章 觉醒",
                    "content": "...",
                    "word_count": 2500
                }
            ]
        },
        "metadata": {
            "model_used": "gpt-4",
            "generation_time": 285,
            "quality_score": 8.5
        },
        "completed_at": "2024-01-01T12:09:45Z"
    }
}
```

### 7.4 取消生成任务
```http
DELETE /api/v1/generate/tasks/task_123456
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "任务已取消"
}
```

### 7.5 获取生成历史
```http
GET /api/v1/generate/tasks?page=1&size=20&status=completed
Authorization: Bearer {access_token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [
            {
                "task_id": "task_123456",
                "task_name": "生成科幻小说",
                "task_type": "novel",
                "status": "completed",
                "created_at": "2024-01-01T12:00:00Z",
                "completed_at": "2024-01-01T12:09:45Z"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 45,
            "pages": 3
        }
    }
}
```

## 8. 状态码定义

### 8.1 成功状态码
- `200` - 请求成功
- `201` - 创建成功
- `204` - 删除成功

### 8.2 客户端错误
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `422` - 参数验证失败
- `429` - 请求频率限制

### 8.3 服务器错误
- `500` - 服务器内部错误
- `502` - 网关错误
- `503` - 服务不可用
- `504` - 网关超时

这个API接口设计提供了完整的RESTful API规范，涵盖了认证、用户管理、内容管理、AI生成等核心功能，具有良好的扩展性和维护性。
