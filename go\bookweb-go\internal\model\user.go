package model

import (
	"time"
	"gorm.io/gorm"
)

type User struct {
	ID              uint           `json:"id" gorm:"primaryKey"`
	Username        string         `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Email           string         `json:"email" gorm:"uniqueIndex;size:100;not null"`
	PasswordHash    string         `json:"-" gorm:"size:255;not null"`
	Nickname        string         `json:"nickname" gorm:"size:100"`
	AvatarURL       string         `json:"avatar_url" gorm:"size:255"`
	Status          int            `json:"status" gorm:"default:1"` // 1:正常 0:禁用
	QuotaDaily      int            `json:"quota_daily" gorm:"default:10"`
	QuotaUsedToday  int            `json:"quota_used_today" gorm:"default:0"`
	LastLoginAt     *time.Time     `json:"last_login_at"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Novels []Novel `json:"novels,omitempty" gorm:"foreignKey:UserID"`
}

func (User) TableName() string {
	return "users"
}
