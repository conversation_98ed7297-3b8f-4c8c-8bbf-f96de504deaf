# Golang 高性能 API 方案设计

## 支持 50 人同时在线 AI 小说生成的 Go 语言架构

## 1. 技术架构概览

### 1.1 核心技术栈

```yaml
Web框架:
  - Gin: 高性能HTTP Web框架
  - Fiber: 极速Web框架 (可选)
  - Echo: 轻量级框架 (可选)

数据库:
  - GORM: ORM框架
  - MySQL: 主数据存储
  - Redis: 缓存和会话

异步处理:
  - Asynq: Redis-based任务队列
  - Goroutines: 并发处理
  - Channels: 协程通信

HTTP客户端:
  - Resty: HTTP客户端库
  - net/http: 标准库

WebSocket:
  - Gorilla WebSocket: 实时通信
  - 原生net/http: WebSocket支持

配置管理:
  - Viper: 配置管理
  - Cobra: CLI工具

监控日志:
  - Logrus/Zap: 结构化日志
  - Prometheus: 指标监控
  - Jaeger: 链路追踪
```

### 1.2 项目结构设计

```
bookweb-go/
├── cmd/
│   ├── api/                    # API服务入口
│   │   └── main.go
│   ├── worker/                 # 异步任务处理器
│   │   └── main.go
│   └── migrate/                # 数据库迁移工具
│       └── main.go
├── internal/
│   ├── api/                    # API层
│   │   ├── handlers/           # 请求处理器
│   │   │   ├── auth.go
│   │   │   ├── novel.go
│   │   │   ├── chapter.go
│   │   │   └── websocket.go
│   │   ├── middleware/         # 中间件
│   │   │   ├── auth.go
│   │   │   ├── cors.go
│   │   │   ├── ratelimit.go
│   │   │   └── recovery.go
│   │   └── routes/             # 路由定义
│   │       └── routes.go
│   ├── service/                # 业务逻辑层
│   │   ├── auth_service.go
│   │   ├── novel_service.go
│   │   ├── chapter_service.go
│   │   └── ai_service.go
│   ├── repository/             # 数据访问层
│   │   ├── user_repo.go
│   │   ├── novel_repo.go
│   │   └── chapter_repo.go
│   ├── model/                  # 数据模型
│   │   ├── user.go
│   │   ├── novel.go
│   │   └── chapter.go
│   ├── dto/                    # 数据传输对象
│   │   ├── request/
│   │   └── response/
│   ├── config/                 # 配置管理
│   │   └── config.go
│   ├── database/               # 数据库连接
│   │   ├── mysql.go
│   │   └── redis.go
│   ├── cache/                  # 缓存管理
│   │   └── redis_cache.go
│   ├── queue/                  # 任务队列
│   │   └── asynq_client.go
│   ├── utils/                  # 工具函数
│   │   ├── jwt.go
│   │   ├── hash.go
│   │   └── validator.go
│   └── worker/                 # 异步任务处理
│       ├── ai_worker.go
│       └── notification_worker.go
├── pkg/                        # 公共包
│   ├── logger/                 # 日志包
│   ├── errors/                 # 错误处理
│   └── response/               # 统一响应
├── configs/                    # 配置文件
│   ├── config.yaml
│   └── config.prod.yaml
├── migrations/                 # 数据库迁移
├── scripts/                    # 脚本文件
├── docker/                     # Docker配置
├── docs/                       # 文档
├── go.mod
├── go.sum
├── Makefile
└── README.md
```

## 2. 核心代码实现

### 2.1 主应用入口

```go
// cmd/api/main.go
package main

import (
    "context"
    "log"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"

    "github.com/gin-gonic/gin"
    "bookweb/internal/api/routes"
    "bookweb/internal/config"
    "bookweb/internal/database"
    "bookweb/pkg/logger"
)

func main() {
    // 加载配置
    cfg, err := config.Load()
    if err != nil {
        log.Fatal("Failed to load config:", err)
    }

    // 初始化日志
    logger.Init(cfg.Log.Level)

    // 初始化数据库
    db, err := database.InitMySQL(cfg.Database.MySQL)
    if err != nil {
        log.Fatal("Failed to connect to MySQL:", err)
    }
    defer database.CloseMySQL()

    // 初始化Redis
    rdb, err := database.InitRedis(cfg.Database.Redis)
    if err != nil {
        log.Fatal("Failed to connect to Redis:", err)
    }
    defer database.CloseRedis()

    // 设置Gin模式
    if cfg.App.Mode == "production" {
        gin.SetMode(gin.ReleaseMode)
    }

    // 创建路由
    router := routes.SetupRoutes(cfg, db, rdb)

    // 创建HTTP服务器
    srv := &http.Server{
        Addr:         ":" + cfg.App.Port,
        Handler:      router,
        ReadTimeout:  time.Duration(cfg.App.ReadTimeout) * time.Second,
        WriteTimeout: time.Duration(cfg.App.WriteTimeout) * time.Second,
        IdleTimeout:  time.Duration(cfg.App.IdleTimeout) * time.Second,
    }

    // 启动服务器
    go func() {
        logger.Info("Server starting on port " + cfg.App.Port)
        if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            log.Fatal("Failed to start server:", err)
        }
    }()

    // 优雅关闭
    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit

    logger.Info("Server shutting down...")

    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    if err := srv.Shutdown(ctx); err != nil {
        log.Fatal("Server forced to shutdown:", err)
    }

    logger.Info("Server exited")
}
```

### 2.2 配置管理

```go
// internal/config/config.go
package config

import (
    "github.com/spf13/viper"
)

type Config struct {
    App      AppConfig      `mapstructure:"app"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    JWT      JWTConfig      `mapstructure:"jwt"`
    AI       AIConfig       `mapstructure:"ai"`
    Log      LogConfig      `mapstructure:"log"`
}

type AppConfig struct {
    Name         string `mapstructure:"name"`
    Mode         string `mapstructure:"mode"`
    Port         string `mapstructure:"port"`
    ReadTimeout  int    `mapstructure:"read_timeout"`
    WriteTimeout int    `mapstructure:"write_timeout"`
    IdleTimeout  int    `mapstructure:"idle_timeout"`
}

type DatabaseConfig struct {
    MySQL MySQLConfig `mapstructure:"mysql"`
    Redis RedisConfig `mapstructure:"redis"`
}

type MySQLConfig struct {
    Host         string `mapstructure:"host"`
    Port         int    `mapstructure:"port"`
    Username     string `mapstructure:"username"`
    Password     string `mapstructure:"password"`
    Database     string `mapstructure:"database"`
    MaxOpenConns int    `mapstructure:"max_open_conns"`
    MaxIdleConns int    `mapstructure:"max_idle_conns"`
    MaxLifetime  int    `mapstructure:"max_lifetime"`
}

type RedisConfig struct {
    Host     string `mapstructure:"host"`
    Port     int    `mapstructure:"port"`
    Password string `mapstructure:"password"`
    DB       int    `mapstructure:"db"`
    PoolSize int    `mapstructure:"pool_size"`
}

type JWTConfig struct {
    Secret     string `mapstructure:"secret"`
    ExpireTime int    `mapstructure:"expire_time"`
}

type AIConfig struct {
    Endpoints []AIEndpoint `mapstructure:"endpoints"`
    Timeout   int          `mapstructure:"timeout"`
    MaxRetry  int          `mapstructure:"max_retry"`
}

type AIEndpoint struct {
    Name     string `mapstructure:"name"`
    Provider string `mapstructure:"provider"` // openai, anthropic, etc.
    URL      string `mapstructure:"url"`
    APIKey   string `mapstructure:"api_key"`
    Weight   int    `mapstructure:"weight"`
    Model    string `mapstructure:"model"`    // 模型名称
}

type LogConfig struct {
    Level  string `mapstructure:"level"`
    Format string `mapstructure:"format"`
}

func Load() (*Config, error) {
    viper.SetConfigName("config")
    viper.SetConfigType("yaml")
    viper.AddConfigPath("./configs")
    viper.AddConfigPath(".")

    // 设置默认值
    setDefaults()

    // 读取环境变量
    viper.AutomaticEnv()

    if err := viper.ReadInConfig(); err != nil {
        return nil, err
    }

    var config Config
    if err := viper.Unmarshal(&config); err != nil {
        return nil, err
    }

    return &config, nil
}

func setDefaults() {
    viper.SetDefault("app.name", "BookWeb")
    viper.SetDefault("app.mode", "development")
    viper.SetDefault("app.port", "8080")
    viper.SetDefault("app.read_timeout", 60)
    viper.SetDefault("app.write_timeout", 60)
    viper.SetDefault("app.idle_timeout", 120)

    viper.SetDefault("database.mysql.max_open_conns", 100)
    viper.SetDefault("database.mysql.max_idle_conns", 10)
    viper.SetDefault("database.mysql.max_lifetime", 3600)

    viper.SetDefault("redis.pool_size", 100)

    viper.SetDefault("jwt.expire_time", 3600)

    viper.SetDefault("ai.timeout", 120)
    viper.SetDefault("ai.max_retry", 3)

    viper.SetDefault("log.level", "info")
    viper.SetDefault("log.format", "json")
}
```

### 2.3 数据模型

```go
// internal/model/user.go
package model

import (
    "time"
    "gorm.io/gorm"
)

type User struct {
    ID              uint           `json:"id" gorm:"primaryKey"`
    Username        string         `json:"username" gorm:"uniqueIndex;size:50;not null"`
    Email           string         `json:"email" gorm:"uniqueIndex;size:100;not null"`
    PasswordHash    string         `json:"-" gorm:"size:255;not null"`
    Nickname        string         `json:"nickname" gorm:"size:100"`
    AvatarURL       string         `json:"avatar_url" gorm:"size:255"`
    Status          int            `json:"status" gorm:"default:1"` // 1:正常 0:禁用
    QuotaDaily      int            `json:"quota_daily" gorm:"default:10"`
    QuotaUsedToday  int            `json:"quota_used_today" gorm:"default:0"`
    LastLoginAt     *time.Time     `json:"last_login_at"`
    CreatedAt       time.Time      `json:"created_at"`
    UpdatedAt       time.Time      `json:"updated_at"`
    DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`

    // 关联关系
    Novels []Novel `json:"novels,omitempty" gorm:"foreignKey:UserID"`
}

// internal/model/novel.go
package model

import (
    "time"
    "gorm.io/gorm"
)

type Novel struct {
    ID                uint           `json:"id" gorm:"primaryKey"`
    UserID            uint           `json:"user_id" gorm:"not null;index"`
    Title             string         `json:"title" gorm:"size:200;not null"`
    Description       string         `json:"description" gorm:"type:text"`
    Genre             string         `json:"genre" gorm:"size:50"`
    Status            int            `json:"status" gorm:"default:0"` // 0:创建中 1:完成 2:暂停
    TotalChapters     int            `json:"total_chapters" gorm:"default:0"`
    CompletedChapters int            `json:"completed_chapters" gorm:"default:0"`
    TotalWords        int            `json:"total_words" gorm:"default:0"`
    CreatedAt         time.Time      `json:"created_at"`
    UpdatedAt         time.Time      `json:"updated_at"`
    DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`

    // 关联关系
    User     User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
    Chapters []Chapter `json:"chapters,omitempty" gorm:"foreignKey:NovelID"`
}

// internal/model/chapter.go
package model

import (
    "time"
    "gorm.io/gorm"
)

type Chapter struct {
    ID             uint           `json:"id" gorm:"primaryKey"`
    NovelID        uint           `json:"novel_id" gorm:"not null;index"`
    ChapterNumber  int            `json:"chapter_number" gorm:"not null"`
    Title          string         `json:"title" gorm:"size:200;not null"`
    Content        string         `json:"content" gorm:"type:longtext"`
    WordCount      int            `json:"word_count" gorm:"default:0"`
    Status         int            `json:"status" gorm:"default:0"` // 0:生成中 1:完成 2:失败
    GenerationTime int            `json:"generation_time"` // 生成耗时(秒)
    CreatedAt      time.Time      `json:"created_at"`
    UpdatedAt      time.Time      `json:"updated_at"`
    DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

    // 关联关系
    Novel Novel `json:"novel,omitempty" gorm:"foreignKey:NovelID"`
}

// 确保唯一性
func (Chapter) TableName() string {
    return "chapters"
}

// 添加唯一索引
func (c *Chapter) BeforeCreate(tx *gorm.DB) error {
    // 可以在这里添加创建前的逻辑
    return nil
}
```

### 2.4 基于 LangChain Go 的 AI 服务调用

```go
// internal/service/ai_service.go
package service

import (
    "context"
    "fmt"
    "math/rand"
    "strings"
    "time"

    "github.com/tmc/langchaingo/llms"
    "github.com/tmc/langchaingo/llms/openai"
    "github.com/tmc/langchaingo/llms/anthropic"
    "github.com/tmc/langchaingo/schema"
    "bookweb/internal/config"
    "bookweb/pkg/logger"
    "bookweb/pkg/metrics"
)

type AIService struct {
    config      *config.AIConfig
    providers   map[string]llms.Model
    retryConfig RetryConfig
}

type RetryConfig struct {
    MaxRetries      int
    InitialDelay    time.Duration
    MaxDelay        time.Duration
    BackoffFactor   float64
    TimeoutDuration time.Duration
}

type AIRequest struct {
    Prompt      string                 `json:"prompt"`
    MaxTokens   int                    `json:"max_tokens"`
    Temperature float64                `json:"temperature"`
    ChapterInfo map[string]interface{} `json:"chapter_info"`
}

type AIResponse struct {
    Content   string        `json:"content"`
    WordCount int           `json:"word_count"`
    Success   bool          `json:"success"`
    Error     string        `json:"error,omitempty"`
    Provider  string        `json:"provider"`
    Duration  time.Duration `json:"duration"`
}

func NewAIService(cfg *config.AIConfig) (*AIService, error) {
    providers := make(map[string]llms.Model)

    // 初始化各个AI提供商
    for _, endpoint := range cfg.Endpoints {
        var model llms.Model
        var err error

        switch strings.ToLower(endpoint.Provider) {
        case "openai":
            model, err = openai.New(
                openai.WithToken(endpoint.APIKey),
                openai.WithBaseURL(endpoint.URL),
            )
        case "anthropic":
            model, err = anthropic.New(
                anthropic.WithToken(endpoint.APIKey),
            )
        default:
            logger.Warn("Unsupported AI provider", "provider", endpoint.Provider)
            continue
        }

        if err != nil {
            logger.Error("Failed to initialize AI provider",
                "provider", endpoint.Provider, "error", err)
            continue
        }

        providers[endpoint.Name] = model
        logger.Info("AI provider initialized", "name", endpoint.Name, "provider", endpoint.Provider)
    }

    if len(providers) == 0 {
        return nil, fmt.Errorf("no AI providers available")
    }

    retryConfig := RetryConfig{
        MaxRetries:      cfg.MaxRetry,
        InitialDelay:    2 * time.Second,
        MaxDelay:        30 * time.Second,
        BackoffFactor:   2.0,
        TimeoutDuration: time.Duration(cfg.Timeout) * time.Second,
    }

    return &AIService{
        config:      cfg,
        providers:   providers,
        retryConfig: retryConfig,
    }, nil
}

func (s *AIService) GenerateChapter(ctx context.Context, prompt string, chapterInfo map[string]interface{}) (*AIResponse, error) {
    startTime := time.Now()

    // 创建带超时的上下文
    timeoutCtx, cancel := context.WithTimeout(ctx, s.retryConfig.TimeoutDuration)
    defer cancel()

    // 选择AI提供商
    endpoint := s.selectEndpoint()
    provider, exists := s.providers[endpoint.Name]
    if !exists {
        return &AIResponse{
            Success: false,
            Error:   fmt.Sprintf("provider %s not available", endpoint.Name),
        }, fmt.Errorf("provider not found: %s", endpoint.Name)
    }

    // 构建完整的提示词
    fullPrompt := s.buildPrompt(prompt, chapterInfo)

    // 执行带重试的AI请求
    response, err := s.executeWithRetry(timeoutCtx, provider, fullPrompt, endpoint)

    duration := time.Since(startTime)

    // 记录指标
    status := "success"
    if err != nil {
        status = "error"
    }
    metrics.RecordAIGeneration(endpoint.Name, status, duration)

    if err != nil {
        logger.Error("AI generation failed after retries",
            "endpoint", endpoint.Name,
            "error", err,
            "duration", duration)
        return &AIResponse{
            Success:  false,
            Error:    err.Error(),
            Provider: endpoint.Name,
            Duration: duration,
        }, err
    }

    aiResp := &AIResponse{
        Content:   response,
        WordCount: len(response),
        Success:   true,
        Provider:  endpoint.Name,
        Duration:  duration,
    }

    logger.Info("AI chapter generated successfully",
        "endpoint", endpoint.Name,
        "word_count", aiResp.WordCount,
        "duration", duration)

    return aiResp, nil
}

func (s *AIService) executeWithRetry(ctx context.Context, provider llms.Model, prompt string, endpoint config.AIEndpoint) (string, error) {
    var lastErr error
    delay := s.retryConfig.InitialDelay

    for attempt := 0; attempt <= s.retryConfig.MaxRetries; attempt++ {
        if attempt > 0 {
            // 等待重试延迟
            select {
            case <-ctx.Done():
                return "", ctx.Err()
            case <-time.After(delay):
                // 指数退避
                delay = time.Duration(float64(delay) * s.retryConfig.BackoffFactor)
                if delay > s.retryConfig.MaxDelay {
                    delay = s.retryConfig.MaxDelay
                }
            }

            logger.Info("Retrying AI request",
                "attempt", attempt+1,
                "max_retries", s.retryConfig.MaxRetries,
                "endpoint", endpoint.Name)
        }

        // 执行AI请求
        response, err := s.callAI(ctx, provider, prompt, endpoint)
        if err == nil {
            if attempt > 0 {
                logger.Info("AI request succeeded after retry",
                    "attempt", attempt+1,
                    "endpoint", endpoint.Name)
            }
            return response, nil
        }

        lastErr = err

        // 检查是否应该重试
        if !s.shouldRetry(err) {
            logger.Error("Non-retryable error, stopping retries",
                "error", err,
                "endpoint", endpoint.Name)
            break
        }

        logger.Warn("AI request failed, will retry",
            "attempt", attempt+1,
            "error", err,
            "endpoint", endpoint.Name)
    }

    return "", fmt.Errorf("AI request failed after %d retries: %w",
        s.retryConfig.MaxRetries, lastErr)
}

func (s *AIService) callAI(ctx context.Context, provider llms.Model, prompt string, endpoint config.AIEndpoint) (string, error) {
    // 构建消息
    messages := []schema.ChatMessage{
        schema.HumanChatMessage{Content: prompt},
    }

    // 设置生成选项
    options := []llms.CallOption{
        llms.WithMaxTokens(4000),
        llms.WithTemperature(0.7),
        llms.WithTopP(0.9),
    }

    // 调用AI模型
    response, err := provider.GenerateContent(ctx, messages, options...)
    if err != nil {
        return "", fmt.Errorf("AI generation failed: %w", err)
    }

    if len(response.Choices) == 0 {
        return "", fmt.Errorf("no response choices returned")
    }

    content := response.Choices[0].Content
    if content == "" {
        return "", fmt.Errorf("empty response content")
    }

    return content, nil
}

func (s *AIService) shouldRetry(err error) bool {
    if err == nil {
        return false
    }

    errStr := strings.ToLower(err.Error())

    // 可重试的错误类型
    retryableErrors := []string{
        "timeout",
        "connection",
        "network",
        "rate limit",
        "429",
        "500",
        "502",
        "503",
        "504",
        "temporary",
        "context deadline exceeded",
        "connection reset",
        "connection refused",
    }

    for _, retryable := range retryableErrors {
        if strings.Contains(errStr, retryable) {
            return true
        }
    }

    return false
}

func (s *AIService) buildPrompt(basePrompt string, chapterInfo map[string]interface{}) string {
    var promptBuilder strings.Builder

    // 添加系统提示
    promptBuilder.WriteString("你是一个专业的小说作家，请根据以下要求生成高质量的小说章节内容。\n\n")

    // 添加章节信息
    if chapterInfo != nil {
        if title, ok := chapterInfo["title"].(string); ok && title != "" {
            promptBuilder.WriteString(fmt.Sprintf("章节标题: %s\n", title))
        }
        if genre, ok := chapterInfo["genre"].(string); ok && genre != "" {
            promptBuilder.WriteString(fmt.Sprintf("小说类型: %s\n", genre))
        }
        if style, ok := chapterInfo["style"].(string); ok && style != "" {
            promptBuilder.WriteString(fmt.Sprintf("写作风格: %s\n", style))
        }
        if wordCount, ok := chapterInfo["target_words"].(int); ok && wordCount > 0 {
            promptBuilder.WriteString(fmt.Sprintf("目标字数: %d字\n", wordCount))
        }
    }

    promptBuilder.WriteString("\n用户需求:\n")
    promptBuilder.WriteString(basePrompt)
    promptBuilder.WriteString("\n\n请生成完整的章节内容，要求情节连贯、人物生动、语言流畅。")

    return promptBuilder.String()
}

func (s *AIService) selectEndpoint() config.AIEndpoint {
    if len(s.config.Endpoints) == 1 {
        return s.config.Endpoints[0]
    }

    // 基于权重选择端点
    totalWeight := 0
    for _, ep := range s.config.Endpoints {
        totalWeight += ep.Weight
    }

    r := rand.Intn(totalWeight)
    for _, ep := range s.config.Endpoints {
        r -= ep.Weight
        if r <= 0 {
            return ep
        }
    }

    return s.config.Endpoints[0]
}

// HealthCheck 检查AI提供商健康状态
func (s *AIService) HealthCheck(ctx context.Context) map[string]bool {
    results := make(map[string]bool)

    for name, provider := range s.providers {
        // 发送简单的测试请求
        testPrompt := "Hello, this is a health check."
        messages := []schema.ChatMessage{
            schema.HumanChatMessage{Content: testPrompt},
        }

        options := []llms.CallOption{
            llms.WithMaxTokens(10),
            llms.WithTemperature(0.1),
        }

        // 设置短超时
        testCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
        _, err := provider.GenerateContent(testCtx, messages, options...)
        cancel()

        results[name] = err == nil

        if err != nil {
            logger.Warn("AI provider health check failed", "provider", name, "error", err)
        } else {
            logger.Debug("AI provider health check passed", "provider", name)
        }
    }

    return results
}

// GetProviderStats 获取提供商统计信息
func (s *AIService) GetProviderStats() map[string]interface{} {
    stats := make(map[string]interface{})

    for _, endpoint := range s.config.Endpoints {
        stats[endpoint.Name] = map[string]interface{}{
            "provider": endpoint.Provider,
            "model":    endpoint.Model,
            "weight":   endpoint.Weight,
            "healthy":  true, // 可以从健康检查结果获取
        }
    }

    return stats
}
```

### 2.5 LangChain Go 高级重试机制

```go
// internal/service/ai_retry_service.go
package service

import (
    "context"
    "fmt"
    "time"

    "github.com/cenkalti/backoff/v4"
    "github.com/tmc/langchaingo/llms"
    "github.com/tmc/langchaingo/schema"
    "bookweb/pkg/logger"
)

// AdvancedRetryService 高级重试服务
type AdvancedRetryService struct {
    providers map[string]llms.Model
    config    *RetryServiceConfig
}

type RetryServiceConfig struct {
    MaxRetries        int
    InitialInterval   time.Duration
    MaxInterval       time.Duration
    MaxElapsedTime    time.Duration
    Multiplier        float64
    RandomizationFactor float64
    CircuitBreakerConfig CircuitBreakerConfig
}

type CircuitBreakerConfig struct {
    MaxFailures     int
    ResetTimeout    time.Duration
    FailureThreshold float64
}

func NewAdvancedRetryService(providers map[string]llms.Model, config *RetryServiceConfig) *AdvancedRetryService {
    return &AdvancedRetryService{
        providers: providers,
        config:    config,
    }
}

func (s *AdvancedRetryService) GenerateWithAdvancedRetry(ctx context.Context, prompt string, providerName string) (string, error) {
    provider, exists := s.providers[providerName]
    if !exists {
        return "", fmt.Errorf("provider %s not found", providerName)
    }

    // 配置指数退避
    exponentialBackoff := backoff.NewExponentialBackOff()
    exponentialBackoff.InitialInterval = s.config.InitialInterval
    exponentialBackoff.MaxInterval = s.config.MaxInterval
    exponentialBackoff.MaxElapsedTime = s.config.MaxElapsedTime
    exponentialBackoff.Multiplier = s.config.Multiplier
    exponentialBackoff.RandomizationFactor = s.config.RandomizationFactor

    // 带上下文的退避
    contextBackoff := backoff.WithContext(exponentialBackoff, ctx)

    var result string
    operation := func() error {
        messages := []schema.ChatMessage{
            schema.HumanChatMessage{Content: prompt},
        }

        options := []llms.CallOption{
            llms.WithMaxTokens(4000),
            llms.WithTemperature(0.7),
        }

        response, err := provider.GenerateContent(ctx, messages, options...)
        if err != nil {
            logger.Warn("AI generation attempt failed", "provider", providerName, "error", err)
            return err
        }

        if len(response.Choices) == 0 {
            return fmt.Errorf("no response choices returned")
        }

        result = response.Choices[0].Content
        return nil
    }

    // 执行带重试的操作
    err := backoff.Retry(operation, contextBackoff)
    if err != nil {
        logger.Error("AI generation failed after all retries",
            "provider", providerName,
            "error", err)
        return "", err
    }

    logger.Info("AI generation succeeded",
        "provider", providerName,
        "content_length", len(result))

    return result, nil
}

// GenerateWithFailover 带故障转移的生成
func (s *AdvancedRetryService) GenerateWithFailover(ctx context.Context, prompt string, providerNames []string) (string, error) {
    var lastErr error

    for _, providerName := range providerNames {
        logger.Info("Attempting AI generation", "provider", providerName)

        result, err := s.GenerateWithAdvancedRetry(ctx, prompt, providerName)
        if err == nil {
            logger.Info("AI generation succeeded with provider", "provider", providerName)
            return result, nil
        }

        lastErr = err
        logger.Warn("AI generation failed, trying next provider",
            "failed_provider", providerName,
            "error", err)
    }

    return "", fmt.Errorf("all providers failed, last error: %w", lastErr)
}
```

### 2.6 Go Modules 依赖配置

```go
// go.mod
module bookweb

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/hibiken/asynq v0.24.1
    github.com/go-redis/redis/v8 v8.11.5
    github.com/spf13/viper v1.16.0
    github.com/spf13/cobra v1.7.0
    github.com/sirupsen/logrus v1.9.3
    github.com/prometheus/client_golang v1.16.0
    github.com/gorilla/websocket v1.5.0
    github.com/gin-contrib/cors v1.4.0
    github.com/golang-jwt/jwt/v5 v5.0.0
    gorm.io/gorm v1.25.4
    gorm.io/driver/mysql v1.5.1

    // LangChain Go 相关依赖
    github.com/tmc/langchaingo v0.1.7
    github.com/cenkalti/backoff/v4 v4.2.1

    // 测试依赖
    github.com/stretchr/testify v1.8.4
    github.com/go-playground/validator/v10 v10.15.1
)

require (
    github.com/bytedance/sonic v1.9.1 // indirect
    github.com/cespare/xxhash/v2 v2.2.0 // indirect
    github.com/chenzhuoyu/base64x v0.0.0-20221115062448-fe3a3abad311 // indirect
    github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
    github.com/gabriel-vasile/mimetype v1.4.2 // indirect
    github.com/gin-contrib/sse v0.1.0 // indirect
    github.com/go-playground/locales v0.14.1 // indirect
    github.com/go-playground/universal-translator v0.18.1 // indirect
    github.com/go-sql-driver/mysql v1.7.0 // indirect
    github.com/goccy/go-json v0.10.2 // indirect
    github.com/google/uuid v1.3.0 // indirect
    github.com/jinzhu/inflection v1.0.0 // indirect
    github.com/jinzhu/now v1.1.5 // indirect
    github.com/json-iterator/go v1.1.12 // indirect
    github.com/klauspost/cpuid/v2 v2.2.4 // indirect
    github.com/leodido/go-urn v1.2.4 // indirect
    github.com/mattn/go-isatty v0.0.19 // indirect
    github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
    github.com/modern-go/reflect2 v1.0.2 // indirect
    github.com/pelletier/go-toml/v2 v2.0.8 // indirect
    github.com/robfig/cron/v3 v3.0.1 // indirect
    github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
    github.com/ugorji/go/codec v1.2.11 // indirect
    golang.org/x/arch v0.3.0 // indirect
    golang.org/x/crypto v0.9.0 // indirect
    golang.org/x/net v0.10.0 // indirect
    golang.org/x/sys v0.8.0 // indirect
    golang.org/x/text v0.9.0 // indirect
    golang.org/x/time v0.3.0 // indirect
    google.golang.org/protobuf v1.30.0 // indirect
    gopkg.in/yaml.v3 v3.0.1 // indirect
)
```

### 2.7 使用示例和测试

```go
// internal/service/ai_service_test.go
package service

import (
    "context"
    "testing"
    "time"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "bookweb/internal/config"
)

func TestAIService_GenerateChapter(t *testing.T) {
    // 配置测试用的AI服务
    cfg := &config.AIConfig{
        Timeout:  120,
        MaxRetry: 3,
        Endpoints: []config.AIEndpoint{
            {
                Name:     "test-openai",
                Provider: "openai",
                URL:      "https://api.openai.com/v1",
                APIKey:   "test-key",
                Model:    "gpt-3.5-turbo",
                Weight:   1,
            },
        },
    }

    // 注意：这里需要mock LangChain Go的接口
    // 实际测试中应该使用依赖注入和接口mock

    t.Run("successful generation", func(t *testing.T) {
        // 这里应该mock AI服务的响应
        // 由于LangChain Go的复杂性，建议使用集成测试
        t.Skip("需要实际的AI API密钥进行集成测试")
    })

    t.Run("retry mechanism", func(t *testing.T) {
        // 测试重试机制
        t.Skip("需要mock网络错误来测试重试")
    })
}

// 集成测试示例
func TestAIService_Integration(t *testing.T) {
    if testing.Short() {
        t.Skip("跳过集成测试")
    }

    cfg := &config.AIConfig{
        Timeout:  30,
        MaxRetry: 2,
        Endpoints: []config.AIEndpoint{
            {
                Name:     "openai-test",
                Provider: "openai",
                URL:      "https://api.openai.com/v1",
                APIKey:   "your-test-api-key", // 需要真实的API密钥
                Model:    "gpt-3.5-turbo",
                Weight:   1,
            },
        },
    }

    aiService, err := NewAIService(cfg)
    assert.NoError(t, err)
    assert.NotNil(t, aiService)

    ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
    defer cancel()

    prompt := "写一个关于勇敢骑士的短故事开头"
    chapterInfo := map[string]interface{}{
        "title":        "第一章：骑士的觉醒",
        "genre":        "奇幻",
        "style":        "史诗",
        "target_words": 500,
    }

    response, err := aiService.GenerateChapter(ctx, prompt, chapterInfo)

    if err != nil {
        t.Logf("AI generation failed (expected in CI): %v", err)
        return
    }

    assert.True(t, response.Success)
    assert.NotEmpty(t, response.Content)
    assert.Greater(t, response.WordCount, 0)
    assert.NotEmpty(t, response.Provider)
}
```

### 2.8 性能优化配置

```go
// internal/service/ai_pool_service.go
package service

import (
    "context"
    "sync"
    "time"

    "github.com/tmc/langchaingo/llms"
    "bookweb/pkg/logger"
)

// AIConnectionPool AI连接池
type AIConnectionPool struct {
    providers map[string]*ProviderPool
    mu        sync.RWMutex
}

type ProviderPool struct {
    models   []llms.Model
    current  int
    mu       sync.Mutex
    maxSize  int
    provider string
}

func NewAIConnectionPool(maxPoolSize int) *AIConnectionPool {
    return &AIConnectionPool{
        providers: make(map[string]*ProviderPool),
    }
}

func (p *AIConnectionPool) AddProvider(name, provider string, models []llms.Model, maxSize int) {
    p.mu.Lock()
    defer p.mu.Unlock()

    p.providers[name] = &ProviderPool{
        models:   models,
        current:  0,
        maxSize:  maxSize,
        provider: provider,
    }

    logger.Info("Added AI provider to pool",
        "name", name,
        "provider", provider,
        "pool_size", len(models))
}

func (p *AIConnectionPool) GetModel(providerName string) (llms.Model, error) {
    p.mu.RLock()
    pool, exists := p.providers[providerName]
    p.mu.RUnlock()

    if !exists {
        return nil, fmt.Errorf("provider %s not found in pool", providerName)
    }

    pool.mu.Lock()
    defer pool.mu.Unlock()

    if len(pool.models) == 0 {
        return nil, fmt.Errorf("no models available for provider %s", providerName)
    }

    // 轮询选择模型
    model := pool.models[pool.current]
    pool.current = (pool.current + 1) % len(pool.models)

    return model, nil
}

// 预热连接池
func (p *AIConnectionPool) WarmUp(ctx context.Context) error {
    p.mu.RLock()
    defer p.mu.RUnlock()

    for name, pool := range p.providers {
        for i, model := range pool.models {
            go func(providerName string, modelIndex int, m llms.Model) {
                warmupCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
                defer cancel()

                // 发送预热请求
                _, err := m.GenerateContent(warmupCtx, []schema.ChatMessage{
                    schema.HumanChatMessage{Content: "Hello"},
                }, llms.WithMaxTokens(1))

                if err != nil {
                    logger.Warn("Model warmup failed",
                        "provider", providerName,
                        "model_index", modelIndex,
                        "error", err)
                } else {
                    logger.Debug("Model warmed up successfully",
                        "provider", providerName,
                        "model_index", modelIndex)
                }
            }(name, i, model)
        }
    }

    return nil
}
```

## 🔥 LangChain Go 的优势

### 1. 统一的 AI 接口

- **多提供商支持**: 统一接口支持 OpenAI、Anthropic、Google 等多个 AI 提供商
- **模型切换**: 无需修改业务代码即可切换不同的 AI 模型
- **标准化**: 统一的消息格式和调用方式

### 2. 高级重试机制

- **指数退避**: 使用`cenkalti/backoff`库实现智能重试
- **上下文感知**: 支持 context 取消和超时控制
- **错误分类**: 自动识别可重试和不可重试的错误

### 3. 性能优化

- **连接池**: 复用 AI 模型连接，减少初始化开销
- **并发安全**: 线程安全的提供商管理
- **预热机制**: 启动时预热连接，减少首次请求延迟

### 4. 监控和可观测性

- **详细日志**: 结构化日志记录每次 AI 调用
- **指标收集**: Prometheus 指标监控成功率和延迟
- **健康检查**: 定期检查 AI 提供商可用性

### 5. 故障转移

- **自动切换**: 主提供商失败时自动切换到备用提供商
- **权重分配**: 基于权重的负载均衡
- **熔断机制**: 防止级联故障

### 6. 配置灵活性

```yaml
# 支持多种配置方式
ai:
  timeout: 120
  max_retry: 3
  endpoints:
    - name: "openai-gpt4"
      provider: "openai"
      model: "gpt-4"
      weight: 3
    - name: "claude-sonnet"
      provider: "anthropic"
      model: "claude-3-sonnet-20240229"
      weight: 2
```

### 7. 使用示例

```go
// 简单使用
aiService, _ := NewAIService(config)
response, err := aiService.GenerateChapter(ctx, prompt, chapterInfo)

// 高级重试
retryService := NewAdvancedRetryService(providers, retryConfig)
result, err := retryService.GenerateWithFailover(ctx, prompt, []string{"openai", "claude"})

// 连接池使用
pool := NewAIConnectionPool(10)
model, _ := pool.GetModel("openai")
```

这套基于 LangChain Go 的 AI 请求和超时重试机制提供了：

✅ **企业级可靠性** - 多重故障保护和自动恢复
✅ **高性能** - 连接池和预热机制优化
✅ **易维护** - 统一接口和配置管理
✅ **可观测** - 完整的监控和日志体系
✅ **可扩展** - 支持新增 AI 提供商和模型

相比直接使用 HTTP 客户端，LangChain Go 方案在稳定性、可维护性和功能完整性方面都有显著优势。

### 2.5 异步任务处理

```go
// internal/worker/ai_worker.go
package worker

import (
    "context"
    "encoding/json"
    "fmt"
    "time"

    "github.com/hibiken/asynq"
    "bookweb/internal/model"
    "bookweb/internal/service"
    "bookweb/pkg/logger"
)

const (
    TypeChapterGeneration = "chapter:generation"
)

type ChapterGenerationPayload struct {
    UserID      uint                   `json:"user_id"`
    NovelID     uint                   `json:"novel_id"`
    ChapterID   uint                   `json:"chapter_id"`
    Prompt      string                 `json:"prompt"`
    ChapterInfo map[string]interface{} `json:"chapter_info"`
}

type AIWorker struct {
    aiService      *service.AIService
    chapterService *service.ChapterService
    wsHub          *WebSocketHub
}

func NewAIWorker(aiService *service.AIService, chapterService *service.ChapterService, wsHub *WebSocketHub) *AIWorker {
    return &AIWorker{
        aiService:      aiService,
        chapterService: chapterService,
        wsHub:          wsHub,
    }
}

func (w *AIWorker) ProcessChapterGeneration(ctx context.Context, t *asynq.Task) error {
    var payload ChapterGenerationPayload
    if err := json.Unmarshal(t.Payload(), &payload); err != nil {
        logger.Error("Failed to unmarshal chapter generation payload", "error", err)
        return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
    }

    logger.Info("Processing chapter generation",
        "user_id", payload.UserID,
        "novel_id", payload.NovelID,
        "chapter_id", payload.ChapterID)

    // 更新章节状态为生成中
    if err := w.chapterService.UpdateStatus(payload.ChapterID, 0); err != nil {
        logger.Error("Failed to update chapter status", "error", err)
        return err
    }

    // 发送进度更新
    w.sendProgress(payload.UserID, payload.ChapterID, "processing", 10, "")

    // 调用AI服务生成内容
    startTime := time.Now()
    aiResp, err := w.aiService.GenerateChapter(ctx, payload.Prompt, payload.ChapterInfo)
    generationTime := int(time.Since(startTime).Seconds())

    if err != nil || !aiResp.Success {
        // 生成失败
        logger.Error("Chapter generation failed", "error", err)

        if err := w.chapterService.UpdateStatusWithError(payload.ChapterID, 2, err.Error()); err != nil {
            logger.Error("Failed to update chapter status to failed", "error", err)
        }

        w.sendProgress(payload.UserID, payload.ChapterID, "failed", 0, err.Error())
        return err
    }

    // 生成成功，更新章节内容
    updateData := map[string]interface{}{
        "content":         aiResp.Content,
        "word_count":      aiResp.WordCount,
        "status":          1, // 完成
        "generation_time": generationTime,
    }

    if err := w.chapterService.UpdateChapter(payload.ChapterID, updateData); err != nil {
        logger.Error("Failed to update chapter content", "error", err)
        return err
    }

    // 发送完成通知
    w.sendProgress(payload.UserID, payload.ChapterID, "completed", 100, "")

    logger.Info("Chapter generation completed successfully",
        "user_id", payload.UserID,
        "chapter_id", payload.ChapterID,
        "word_count", aiResp.WordCount,
        "generation_time", generationTime)

    return nil
}

func (w *AIWorker) sendProgress(userID, chapterID uint, status string, progress int, errorMsg string) {
    progressData := map[string]interface{}{
        "type":       "chapter_progress",
        "chapter_id": chapterID,
        "status":     status,
        "progress":   progress,
        "timestamp":  time.Now().Unix(),
    }

    if errorMsg != "" {
        progressData["error"] = errorMsg
    }

    w.wsHub.SendToUser(userID, progressData)
}

// 任务创建函数
func NewChapterGenerationTask(payload ChapterGenerationPayload) (*asynq.Task, error) {
    data, err := json.Marshal(payload)
    if err != nil {
        return nil, err
    }

    return asynq.NewTask(TypeChapterGeneration, data, asynq.MaxRetry(3)), nil
}
```

### 2.6 WebSocket 实时通信

```go
// internal/api/handlers/websocket.go
package handlers

import (
    "encoding/json"
    "net/http"
    "strconv"
    "sync"

    "github.com/gin-gonic/gin"
    "github.com/gorilla/websocket"
    "bookweb/pkg/logger"
)

var upgrader = websocket.Upgrader{
    CheckOrigin: func(r *http.Request) bool {
        return true // 生产环境需要检查Origin
    },
}

type WebSocketHub struct {
    clients    map[uint]*websocket.Conn
    register   chan *Client
    unregister chan *Client
    broadcast  chan []byte
    mutex      sync.RWMutex
}

type Client struct {
    UserID uint
    Conn   *websocket.Conn
}

type Message struct {
    Type string      `json:"type"`
    Data interface{} `json:"data"`
}

func NewWebSocketHub() *WebSocketHub {
    return &WebSocketHub{
        clients:    make(map[uint]*websocket.Conn),
        register:   make(chan *Client),
        unregister: make(chan *Client),
        broadcast:  make(chan []byte),
    }
}

func (h *WebSocketHub) Run() {
    for {
        select {
        case client := <-h.register:
            h.mutex.Lock()
            h.clients[client.UserID] = client.Conn
            h.mutex.Unlock()
            logger.Info("WebSocket client connected", "user_id", client.UserID)

        case client := <-h.unregister:
            h.mutex.Lock()
            if conn, ok := h.clients[client.UserID]; ok {
                delete(h.clients, client.UserID)
                conn.Close()
            }
            h.mutex.Unlock()
            logger.Info("WebSocket client disconnected", "user_id", client.UserID)

        case message := <-h.broadcast:
            h.mutex.RLock()
            for userID, conn := range h.clients {
                if err := conn.WriteMessage(websocket.TextMessage, message); err != nil {
                    logger.Error("WebSocket write error", "error", err, "user_id", userID)
                    conn.Close()
                    delete(h.clients, userID)
                }
            }
            h.mutex.RUnlock()
        }
    }
}

func (h *WebSocketHub) SendToUser(userID uint, data interface{}) {
    h.mutex.RLock()
    conn, exists := h.clients[userID]
    h.mutex.RUnlock()

    if !exists {
        return
    }

    message := Message{
        Type: "progress_update",
        Data: data,
    }

    jsonData, err := json.Marshal(message)
    if err != nil {
        logger.Error("Failed to marshal WebSocket message", "error", err)
        return
    }

    if err := conn.WriteMessage(websocket.TextMessage, jsonData); err != nil {
        logger.Error("Failed to send WebSocket message", "error", err, "user_id", userID)
        h.mutex.Lock()
        delete(h.clients, userID)
        conn.Close()
        h.mutex.Unlock()
    }
}

func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
    // 获取用户ID (从JWT token或查询参数)
    userIDStr := c.Query("user_id")
    userID, err := strconv.ParseUint(userIDStr, 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user_id"})
        return
    }

    // 升级HTTP连接为WebSocket
    conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
    if err != nil {
        logger.Error("WebSocket upgrade failed", "error", err)
        return
    }

    client := &Client{
        UserID: uint(userID),
        Conn:   conn,
    }

    h.hub.register <- client

    // 处理连接
    go h.handleConnection(client)
}

func (h *WebSocketHandler) handleConnection(client *Client) {
    defer func() {
        h.hub.unregister <- client
    }()

    for {
        _, message, err := client.Conn.ReadMessage()
        if err != nil {
            if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
                logger.Error("WebSocket error", "error", err)
            }
            break
        }

        // 处理客户端消息 (心跳包等)
        var msg Message
        if err := json.Unmarshal(message, &msg); err != nil {
            logger.Error("Failed to unmarshal WebSocket message", "error", err)
            continue
        }

        switch msg.Type {
        case "ping":
            // 响应心跳包
            pong := Message{Type: "pong", Data: "pong"}
            if jsonData, err := json.Marshal(pong); err == nil {
                client.Conn.WriteMessage(websocket.TextMessage, jsonData)
            }
        }
    }
}
```

### 2.7 中间件实现

```go
// internal/api/middleware/auth.go
package middleware

import (
    "net/http"
    "strings"

    "github.com/gin-gonic/gin"
    "bookweb/internal/utils"
    "bookweb/pkg/response"
)

func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        if token == "" {
            response.Error(c, http.StatusUnauthorized, "Missing authorization token")
            c.Abort()
            return
        }

        // 移除 "Bearer " 前缀
        if strings.HasPrefix(token, "Bearer ") {
            token = token[7:]
        }

        // 验证JWT token
        claims, err := utils.ParseJWT(token)
        if err != nil {
            response.Error(c, http.StatusUnauthorized, "Invalid token")
            c.Abort()
            return
        }

        // 将用户信息存储到上下文
        c.Set("user_id", claims.UserID)
        c.Set("username", claims.Username)
        c.Next()
    }
}

// internal/api/middleware/ratelimit.go
package middleware

import (
    "net/http"
    "strconv"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/go-redis/redis/v8"
    "bookweb/pkg/response"
)

type RateLimiter struct {
    rdb *redis.Client
}

func NewRateLimiter(rdb *redis.Client) *RateLimiter {
    return &RateLimiter{rdb: rdb}
}

func (rl *RateLimiter) Limit(requests int, window time.Duration) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 获取客户端标识 (IP或用户ID)
        key := c.ClientIP()
        if userID, exists := c.Get("user_id"); exists {
            key = "user:" + strconv.Itoa(int(userID.(uint)))
        }

        // 检查限流
        current, err := rl.rdb.Incr(c.Request.Context(), key).Result()
        if err != nil {
            // Redis错误时允许请求通过
            c.Next()
            return
        }

        if current == 1 {
            // 设置过期时间
            rl.rdb.Expire(c.Request.Context(), key, window)
        }

        if current > int64(requests) {
            response.Error(c, http.StatusTooManyRequests, "Rate limit exceeded")
            c.Abort()
            return
        }

        c.Next()
    }
}

// internal/api/middleware/cors.go
package middleware

import (
    "github.com/gin-contrib/cors"
    "github.com/gin-gonic/gin"
)

func CORSMiddleware() gin.HandlerFunc {
    config := cors.DefaultConfig()
    config.AllowOrigins = []string{"*"} // 生产环境需要限制
    config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
    config.AllowHeaders = []string{"Origin", "Content-Type", "Authorization"}
    config.ExposeHeaders = []string{"Content-Length"}
    config.AllowCredentials = true

    return cors.New(config)
}

// internal/api/middleware/recovery.go
package middleware

import (
    "net/http"

    "github.com/gin-gonic/gin"
    "bookweb/pkg/logger"
    "bookweb/pkg/response"
)

func RecoveryMiddleware() gin.HandlerFunc {
    return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
        logger.Error("Panic recovered", "error", recovered)
        response.Error(c, http.StatusInternalServerError, "Internal server error")
    })
}
```

### 2.8 API 处理器

```go
// internal/api/handlers/chapter.go
package handlers

import (
    "net/http"
    "strconv"

    "github.com/gin-gonic/gin"
    "github.com/hibiken/asynq"
    "bookweb/internal/dto/request"
    "bookweb/internal/dto/response"
    "bookweb/internal/service"
    "bookweb/internal/worker"
    "bookweb/pkg/response"
)

type ChapterHandler struct {
    chapterService *service.ChapterService
    aiService      *service.AIService
    taskClient     *asynq.Client
}

func NewChapterHandler(chapterService *service.ChapterService, aiService *service.AIService, taskClient *asynq.Client) *ChapterHandler {
    return &ChapterHandler{
        chapterService: chapterService,
        aiService:      aiService,
        taskClient:     taskClient,
    }
}

// CreateChapter 创建章节
func (h *ChapterHandler) CreateChapter(c *gin.Context) {
    var req request.CreateChapterRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid request parameters")
        return
    }

    userID := c.GetUint("user_id")

    // 检查用户配额
    if !h.chapterService.CheckUserQuota(userID) {
        response.Error(c, http.StatusForbidden, "Daily quota exceeded")
        return
    }

    // 创建章节记录
    chapter, err := h.chapterService.CreateChapter(userID, &req)
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "Failed to create chapter")
        return
    }

    // 创建异步生成任务
    payload := worker.ChapterGenerationPayload{
        UserID:      userID,
        NovelID:     req.NovelID,
        ChapterID:   chapter.ID,
        Prompt:      req.Prompt,
        ChapterInfo: req.ChapterInfo,
    }

    task, err := worker.NewChapterGenerationTask(payload)
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "Failed to create generation task")
        return
    }

    // 提交任务到队列
    info, err := h.taskClient.Enqueue(task)
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "Failed to enqueue generation task")
        return
    }

    // 更新用户配额
    h.chapterService.UpdateUserQuota(userID)

    resp := response.CreateChapterResponse{
        ChapterID: chapter.ID,
        TaskID:    info.ID,
        Status:    "queued",
        Message:   "Chapter generation task has been queued",
    }

    response.Success(c, resp)
}

// GetChapter 获取章节详情
func (h *ChapterHandler) GetChapter(c *gin.Context) {
    chapterIDStr := c.Param("id")
    chapterID, err := strconv.ParseUint(chapterIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid chapter ID")
        return
    }

    chapter, err := h.chapterService.GetChapterByID(uint(chapterID))
    if err != nil {
        response.Error(c, http.StatusNotFound, "Chapter not found")
        return
    }

    // 检查权限 (用户只能查看自己的章节)
    userID := c.GetUint("user_id")
    if chapter.Novel.UserID != userID {
        response.Error(c, http.StatusForbidden, "Access denied")
        return
    }

    resp := response.ChapterResponse{
        ID:             chapter.ID,
        NovelID:        chapter.NovelID,
        ChapterNumber:  chapter.ChapterNumber,
        Title:          chapter.Title,
        Content:        chapter.Content,
        WordCount:      chapter.WordCount,
        Status:         chapter.Status,
        GenerationTime: chapter.GenerationTime,
        CreatedAt:      chapter.CreatedAt,
        UpdatedAt:      chapter.UpdatedAt,
    }

    response.Success(c, resp)
}

// ListChapters 获取章节列表
func (h *ChapterHandler) ListChapters(c *gin.Context) {
    novelIDStr := c.Query("novel_id")
    novelID, err := strconv.ParseUint(novelIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid novel ID")
        return
    }

    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

    userID := c.GetUint("user_id")

    chapters, total, err := h.chapterService.GetChaptersByNovelID(uint(novelID), userID, page, size)
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "Failed to get chapters")
        return
    }

    var chapterList []response.ChapterResponse
    for _, chapter := range chapters {
        chapterList = append(chapterList, response.ChapterResponse{
            ID:             chapter.ID,
            NovelID:        chapter.NovelID,
            ChapterNumber:  chapter.ChapterNumber,
            Title:          chapter.Title,
            WordCount:      chapter.WordCount,
            Status:         chapter.Status,
            GenerationTime: chapter.GenerationTime,
            CreatedAt:      chapter.CreatedAt,
            UpdatedAt:      chapter.UpdatedAt,
        })
    }

    resp := response.PaginatedResponse{
        Items: chapterList,
        Pagination: response.Pagination{
            Page:  page,
            Size:  size,
            Total: total,
            Pages: (total + size - 1) / size,
        },
    }

    response.Success(c, resp)
}
```

## 3. 部署配置

### 3.1 Docker 配置

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app

# 安装依赖
RUN apk add --no-cache git

# 复制go mod文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main cmd/api/main.go

# 运行阶段
FROM alpine:latest

RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

# 复制二进制文件
COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs

# 暴露端口
EXPOSE 8080

# 运行应用
CMD ["./main"]
```

### 3.2 Docker Compose 配置

```yaml
# docker-compose.yml
version: "3.8"

services:
  # API服务
  api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - APP_MODE=production
      - DATABASE_MYSQL_HOST=mysql
      - DATABASE_MYSQL_PORT=3306
      - DATABASE_MYSQL_USERNAME=bookweb
      - DATABASE_MYSQL_PASSWORD=password123
      - DATABASE_MYSQL_DATABASE=bookweb
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    deploy:
      replicas: 3

  # Worker服务
  worker:
    build: .
    command: ["./worker"]
    environment:
      - APP_MODE=production
      - DATABASE_MYSQL_HOST=mysql
      - DATABASE_MYSQL_PORT=3306
      - DATABASE_MYSQL_USERNAME=bookweb
      - DATABASE_MYSQL_PASSWORD=password123
      - DATABASE_MYSQL_DATABASE=bookweb
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    deploy:
      replicas: 5

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: bookweb
      MYSQL_USER: bookweb
      MYSQL_PASSWORD: password123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

  # Nginx负载均衡
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### 3.3 配置文件

```yaml
# configs/config.yaml
app:
  name: "BookWeb"
  mode: "development"
  port: "8080"
  read_timeout: 60
  write_timeout: 60
  idle_timeout: 120

database:
  mysql:
    host: "localhost"
    port: 3306
    username: "bookweb"
    password: "password123"
    database: "bookweb"
    max_open_conns: 100
    max_idle_conns: 10
    max_lifetime: 3600

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 100

jwt:
  secret: "your-super-secret-key"
  expire_time: 3600

ai:
  timeout: 120
  max_retry: 3
  endpoints:
    - name: "openai-primary"
      provider: "openai"
      url: "https://api.openai.com/v1"
      api_key: "your-openai-api-key"
      model: "gpt-4"
      weight: 3
    - name: "claude-secondary"
      provider: "anthropic"
      url: "https://api.anthropic.com"
      api_key: "your-claude-api-key"
      model: "claude-3-sonnet-20240229"
      weight: 2
    - name: "openai-backup"
      provider: "openai"
      url: "https://api.openai.com/v1"
      api_key: "your-backup-openai-key"
      model: "gpt-3.5-turbo"
      weight: 1

log:
  level: "info"
  format: "json"
```

## 4. 性能优化策略

### 4.1 连接池优化

```go
// internal/database/mysql.go
package database

import (
    "time"
    "gorm.io/driver/mysql"
    "gorm.io/gorm"
    "bookweb/internal/config"
)

var DB *gorm.DB

func InitMySQL(cfg config.MySQLConfig) (*gorm.DB, error) {
    dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
        cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.Database)

    db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
        // 禁用默认事务
        SkipDefaultTransaction: true,
        // 预编译语句
        PrepareStmt: true,
    })
    if err != nil {
        return nil, err
    }

    sqlDB, err := db.DB()
    if err != nil {
        return nil, err
    }

    // 连接池配置
    sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
    sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
    sqlDB.SetConnMaxLifetime(time.Duration(cfg.MaxLifetime) * time.Second)

    DB = db
    return db, nil
}
```

### 4.2 缓存优化

```go
// internal/cache/redis_cache.go
package cache

import (
    "context"
    "encoding/json"
    "time"

    "github.com/go-redis/redis/v8"
)

type RedisCache struct {
    client *redis.Client
}

func NewRedisCache(client *redis.Client) *RedisCache {
    return &RedisCache{client: client}
}

func (r *RedisCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
    data, err := json.Marshal(value)
    if err != nil {
        return err
    }
    return r.client.Set(ctx, key, data, expiration).Err()
}

func (r *RedisCache) Get(ctx context.Context, key string, dest interface{}) error {
    data, err := r.client.Get(ctx, key).Result()
    if err != nil {
        return err
    }
    return json.Unmarshal([]byte(data), dest)
}

func (r *RedisCache) Delete(ctx context.Context, key string) error {
    return r.client.Del(ctx, key).Err()
}

// 批量操作
func (r *RedisCache) MSet(ctx context.Context, pairs map[string]interface{}, expiration time.Duration) error {
    pipe := r.client.Pipeline()

    for key, value := range pairs {
        data, err := json.Marshal(value)
        if err != nil {
            return err
        }
        pipe.Set(ctx, key, data, expiration)
    }

    _, err := pipe.Exec(ctx)
    return err
}

func (r *RedisCache) MGet(ctx context.Context, keys []string) ([]interface{}, error) {
    return r.client.MGet(ctx, keys...).Result()
}
```

### 4.3 并发控制

```go
// internal/utils/concurrency.go
package utils

import (
    "context"
    "sync"
)

// WorkerPool 工作池
type WorkerPool struct {
    workerCount int
    jobQueue    chan func()
    wg          sync.WaitGroup
    ctx         context.Context
    cancel      context.CancelFunc
}

func NewWorkerPool(workerCount int) *WorkerPool {
    ctx, cancel := context.WithCancel(context.Background())
    return &WorkerPool{
        workerCount: workerCount,
        jobQueue:    make(chan func(), workerCount*2),
        ctx:         ctx,
        cancel:      cancel,
    }
}

func (wp *WorkerPool) Start() {
    for i := 0; i < wp.workerCount; i++ {
        wp.wg.Add(1)
        go wp.worker()
    }
}

func (wp *WorkerPool) worker() {
    defer wp.wg.Done()

    for {
        select {
        case job := <-wp.jobQueue:
            job()
        case <-wp.ctx.Done():
            return
        }
    }
}

func (wp *WorkerPool) Submit(job func()) {
    select {
    case wp.jobQueue <- job:
    case <-wp.ctx.Done():
        return
    }
}

func (wp *WorkerPool) Stop() {
    wp.cancel()
    wp.wg.Wait()
}

// 限流器
type RateLimiter struct {
    tokens chan struct{}
}

func NewRateLimiter(rate int) *RateLimiter {
    tokens := make(chan struct{}, rate)
    for i := 0; i < rate; i++ {
        tokens <- struct{}{}
    }
    return &RateLimiter{tokens: tokens}
}

func (rl *RateLimiter) Allow() bool {
    select {
    case <-rl.tokens:
        return true
    default:
        return false
    }
}

func (rl *RateLimiter) Release() {
    select {
    case rl.tokens <- struct{}{}:
    default:
    }
}
```

## 5. 监控与日志

### 5.1 Prometheus 指标监控

```go
// pkg/metrics/prometheus.go
package metrics

import (
    "github.com/gin-gonic/gin"
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promhttp"
    "strconv"
    "time"
)

var (
    // HTTP请求总数
    httpRequestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )

    // HTTP请求持续时间
    httpRequestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "http_request_duration_seconds",
            Help:    "HTTP request duration in seconds",
            Buckets: prometheus.DefBuckets,
        },
        []string{"method", "endpoint"},
    )

    // AI生成任务指标
    aiGenerationTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "ai_generation_total",
            Help: "Total number of AI generation tasks",
        },
        []string{"status", "endpoint"},
    )

    aiGenerationDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "ai_generation_duration_seconds",
            Help:    "AI generation duration in seconds",
            Buckets: []float64{1, 5, 10, 30, 60, 120, 300},
        },
        []string{"endpoint"},
    )

    // 数据库连接池指标
    dbConnectionsInUse = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "db_connections_in_use",
            Help: "Number of database connections currently in use",
        },
    )

    dbConnectionsIdle = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "db_connections_idle",
            Help: "Number of idle database connections",
        },
    )
)

func init() {
    prometheus.MustRegister(
        httpRequestsTotal,
        httpRequestDuration,
        aiGenerationTotal,
        aiGenerationDuration,
        dbConnectionsInUse,
        dbConnectionsIdle,
    )
}

// PrometheusMiddleware Gin中间件
func PrometheusMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()

        c.Next()

        duration := time.Since(start).Seconds()
        status := strconv.Itoa(c.Writer.Status())

        httpRequestsTotal.WithLabelValues(c.Request.Method, c.FullPath(), status).Inc()
        httpRequestDuration.WithLabelValues(c.Request.Method, c.FullPath()).Observe(duration)
    }
}

// MetricsHandler 暴露指标端点
func MetricsHandler() gin.HandlerFunc {
    h := promhttp.Handler()
    return func(c *gin.Context) {
        h.ServeHTTP(c.Writer, c.Request)
    }
}

// RecordAIGeneration 记录AI生成指标
func RecordAIGeneration(endpoint, status string, duration time.Duration) {
    aiGenerationTotal.WithLabelValues(status, endpoint).Inc()
    aiGenerationDuration.WithLabelValues(endpoint).Observe(duration.Seconds())
}

// UpdateDBConnectionMetrics 更新数据库连接指标
func UpdateDBConnectionMetrics(inUse, idle int) {
    dbConnectionsInUse.Set(float64(inUse))
    dbConnectionsIdle.Set(float64(idle))
}
```

### 5.2 结构化日志

```go
// pkg/logger/logger.go
package logger

import (
    "os"
    "github.com/sirupsen/logrus"
)

var log *logrus.Logger

func Init(level string) {
    log = logrus.New()

    // 设置日志级别
    switch level {
    case "debug":
        log.SetLevel(logrus.DebugLevel)
    case "info":
        log.SetLevel(logrus.InfoLevel)
    case "warn":
        log.SetLevel(logrus.WarnLevel)
    case "error":
        log.SetLevel(logrus.ErrorLevel)
    default:
        log.SetLevel(logrus.InfoLevel)
    }

    // 设置JSON格式
    log.SetFormatter(&logrus.JSONFormatter{
        TimestampFormat: "2006-01-02 15:04:05",
    })

    log.SetOutput(os.Stdout)
}

func Debug(msg string, fields ...interface{}) {
    log.WithFields(parseFields(fields...)).Debug(msg)
}

func Info(msg string, fields ...interface{}) {
    log.WithFields(parseFields(fields...)).Info(msg)
}

func Warn(msg string, fields ...interface{}) {
    log.WithFields(parseFields(fields...)).Warn(msg)
}

func Error(msg string, fields ...interface{}) {
    log.WithFields(parseFields(fields...)).Error(msg)
}

func Fatal(msg string, fields ...interface{}) {
    log.WithFields(parseFields(fields...)).Fatal(msg)
}

func parseFields(fields ...interface{}) logrus.Fields {
    result := make(logrus.Fields)
    for i := 0; i < len(fields); i += 2 {
        if i+1 < len(fields) {
            key := fields[i].(string)
            value := fields[i+1]
            result[key] = value
        }
    }
    return result
}
```

## 6. 性能测试与基准

### 6.1 压力测试脚本

```bash
#!/bin/bash
# scripts/load_test.sh

# 基础API测试
echo "Testing basic API endpoints..."

# 登录获取token
TOKEN=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password"}' | jq -r '.data.token')

# 并发创建章节测试
echo "Running concurrent chapter creation test..."
ab -n 1000 -c 50 -H "Authorization: Bearer $TOKEN" \
   -p chapter_data.json -T application/json \
   http://localhost:8080/api/v1/chapters

# WebSocket连接测试
echo "Testing WebSocket connections..."
for i in {1..50}; do
    wscat -c "ws://localhost:8080/ws?user_id=$i" &
done

# 等待测试完成
wait

echo "Load test completed!"
```

### 6.2 性能基准

```go
// benchmark_test.go
package main

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"

    "github.com/gin-gonic/gin"
    "bookweb/internal/api/routes"
)

func BenchmarkCreateChapter(b *testing.B) {
    gin.SetMode(gin.TestMode)
    router := routes.SetupRoutes(nil, nil, nil)

    requestBody := map[string]interface{}{
        "novel_id": 1,
        "title": "Test Chapter",
        "prompt": "Generate a chapter about...",
    }

    jsonData, _ := json.Marshal(requestBody)

    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            req := httptest.NewRequest("POST", "/api/v1/chapters", bytes.NewBuffer(jsonData))
            req.Header.Set("Content-Type", "application/json")
            req.Header.Set("Authorization", "Bearer test-token")

            w := httptest.NewRecorder()
            router.ServeHTTP(w, req)

            if w.Code != http.StatusOK {
                b.Errorf("Expected status 200, got %d", w.Code)
            }
        }
    })
}

func BenchmarkGetChapter(b *testing.B) {
    gin.SetMode(gin.TestMode)
    router := routes.SetupRoutes(nil, nil, nil)

    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            req := httptest.NewRequest("GET", "/api/v1/chapters/1", nil)
            req.Header.Set("Authorization", "Bearer test-token")

            w := httptest.NewRecorder()
            router.ServeHTTP(w, req)

            if w.Code != http.StatusOK {
                b.Errorf("Expected status 200, got %d", w.Code)
            }
        }
    })
}
```

## 🚀 性能优势

1. **高并发处理**: Goroutines 天然支持高并发，单机可处理数万并发连接
2. **内存效率**: Go 的垃圾回收和内存管理，内存占用比 Python/Node.js 低 60%
3. **快速启动**: 编译型语言，冷启动时间<100ms
4. **资源占用低**: CPU 和内存使用效率高，成本更低

## 🔧 架构特点

1. **清晰分层**: Handler -> Service -> Repository，职责分离
2. **依赖注入**: 便于测试和维护，支持接口替换
3. **配置管理**: Viper 统一配置管理，支持多环境
4. **异步处理**: Asynq 任务队列，支持重试和监控
5. **实时通信**: WebSocket 支持，实时推送生成进度
6. **缓存策略**: Redis 多级缓存，提升响应速度
7. **监控完善**: Prometheus 指标 + 结构化日志

## 📊 预期性能指标

- **并发用户**: 支持 50+用户同时在线
- **响应时间**: API 响应时间<100ms
- **吞吐量**: 单机 QPS 可达 5000+
- **AI 生成**: 支持并发 AI 请求，平均生成时间 60-120 秒
- **内存占用**: 运行时内存<512MB
- **CPU 使用**: 正常负载下 CPU<30%

这套 Golang 高性能 API 方案具备了支持 50 人同时在线 AI 小说生成的所有核心功能，包括用户认证、章节管理、异步 AI 生成、实时通信、缓存优化、监控告警等完整功能。相比 Python 方案，在性能、资源占用和并发处理能力方面都有显著优势。
