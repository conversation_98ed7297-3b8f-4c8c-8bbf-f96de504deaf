@echo off
echo Starting BookWeb Go Application...

REM Check if Go is installed
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go is not installed or not in PATH
    echo Please run scripts/install.bat first
    pause
    exit /b 1
)

REM Check if config file exists
if not exist configs\config.yaml (
    echo Error: Configuration file not found
    echo Please create configs\config.yaml based on the example
    pause
    exit /b 1
)

echo Building application...
go build -o bookweb.exe cmd/api/main.go

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Starting server...
bookweb.exe

pause
