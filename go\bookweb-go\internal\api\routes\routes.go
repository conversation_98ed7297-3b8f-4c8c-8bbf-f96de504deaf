package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"

	"bookweb/internal/api/middleware"
	"bookweb/internal/config"
	"bookweb/pkg/response"
)

func SetupRoutes(cfg *config.Config, db *gorm.DB, rdb *redis.Client) *gin.Engine {
	router := gin.New()

	// 添加中间件
	router.Use(middleware.RecoveryMiddleware())
	router.Use(middleware.CORSMiddleware())
	router.Use(gin.Logger())

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		response.Success(c, gin.H{
			"status": "ok",
			"app":    cfg.App.Name,
		})
	})

	// API路由组
	api := router.Group("/api/v1")
	{
		// 用户相关路由
		users := api.Group("/users")
		{
			users.POST("/register", func(c *gin.Context) {
				response.Success(c, gin.H{"message": "register endpoint"})
			})
			users.POST("/login", func(c *gin.Context) {
				response.Success(c, gin.H{"message": "login endpoint"})
			})
		}

		// 小说相关路由
		novels := api.Group("/novels")
		{
			novels.GET("", func(c *gin.Context) {
				response.Success(c, gin.H{"message": "list novels endpoint"})
			})
			novels.POST("", func(c *gin.Context) {
				response.Success(c, gin.H{"message": "create novel endpoint"})
			})
		}

		// 章节相关路由
		chapters := api.Group("/chapters")
		{
			chapters.GET("", func(c *gin.Context) {
				response.Success(c, gin.H{"message": "list chapters endpoint"})
			})
			chapters.POST("", func(c *gin.Context) {
				response.Success(c, gin.H{"message": "create chapter endpoint"})
			})
			chapters.GET("/:id", func(c *gin.Context) {
				response.Success(c, gin.H{"message": "get chapter endpoint"})
			})
		}
	}

	// WebSocket路由
	router.GET("/ws", func(c *gin.Context) {
		response.Success(c, gin.H{"message": "websocket endpoint"})
	})

	return router
}
