app:
  name: "BookWeb"
  mode: "development"
  port: "8080"
  read_timeout: 60
  write_timeout: 60
  idle_timeout: 120

database:
  mysql:
    host: "localhost"
    port: 3306
    username: "bookweb"
    password: "password123"
    database: "bookweb"
    max_open_conns: 100
    max_idle_conns: 10
    max_lifetime: 3600

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 100

jwt:
  secret: "your-super-secret-key"
  expire_time: 3600

ai:
  timeout: 120
  max_retry: 3
  endpoints:
    - name: "openai-primary"
      provider: "openai"
      url: "https://api.openai.com/v1"
      api_key: "your-openai-api-key"
      model: "gpt-4"
      weight: 3
    - name: "claude-secondary"
      provider: "anthropic"
      url: "https://api.anthropic.com"
      api_key: "your-claude-api-key"
      model: "claude-3-sonnet-20240229"
      weight: 2
    - name: "openai-backup"
      provider: "openai"
      url: "https://api.openai.com/v1"
      api_key: "your-backup-openai-key"
      model: "gpt-3.5-turbo"
      weight: 1

log:
  level: "info"
  format: "json"
