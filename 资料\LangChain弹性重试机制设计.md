# LangChain AI请求弹性重试机制设计
## 基于LangChain的第三方API调用重试策略

## 1. LangChain错误类型分析

### 1.1 LangChain常见异常
```python
from langchain.schema import LLMResult
from langchain.llms.base import LLM
from langchain.callbacks.manager import CallbackManagerForLLMRun
import openai
from typing import Optional, List, Any

# LangChain相关异常
langchain_retryable_errors = [
    # OpenAI相关错误
    openai.error.Timeout,
    openai.error.APIError,
    openai.error.APIConnectionError,
    openai.error.RateLimitError,
    openai.error.ServiceUnavailableError,
    
    # 通用网络错误
    ConnectionError,
    TimeoutError,
    
    # HTTP错误
    requests.exceptions.Timeout,
    requests.exceptions.ConnectionError,
    requests.exceptions.HTTPError,
]

langchain_non_retryable_errors = [
    # OpenAI认证和权限错误
    openai.error.AuthenticationError,
    openai.error.PermissionError,
    openai.error.InvalidRequestError,
    
    # 配额和计费错误
    openai.error.InsufficientQuotaError,
    
    # 内容策略错误
    openai.error.ContentPolicyViolationError,
]
```

### 1.2 错误分类处理策略
```yaml
可重试错误处理:
  网络超时:
    - 错误类型: Timeout, APIConnectionError
    - 重试策略: 指数退避
    - 最大重试: 3次
    - 基础延迟: 2秒

  API限流:
    - 错误类型: RateLimitError
    - 重试策略: 固定延迟 + 随机抖动
    - 最大重试: 5次
    - 基础延迟: 60秒

  服务不可用:
    - 错误类型: ServiceUnavailableError, APIError
    - 重试策略: 线性退避
    - 最大重试: 3次
    - 基础延迟: 5秒

不可重试错误:
  认证错误:
    - 立即失败，记录错误日志
    - 通知管理员检查API密钥
  
  内容违规:
    - 立即失败，记录违规内容
    - 可考虑内容过滤后重试
```

## 2. LangChain重试机制实现

### 2.1 自定义LLM包装器
```python
import asyncio
import random
import time
import logging
from typing import Optional, List, Any, Dict
from langchain.llms.base import LLM
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.schema import LLMResult
import openai

class RetryableLLM(LLM):
    """带重试机制的LLM包装器"""
    
    def __init__(self, 
                 base_llm: LLM,
                 max_retries: int = 3,
                 base_delay: float = 2.0,
                 max_delay: float = 60.0,
                 backoff_factor: float = 2.0,
                 jitter: bool = True):
        super().__init__()
        self.base_llm = base_llm
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        self.logger = logging.getLogger(__name__)
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "retry_requests": 0,
            "success_after_retry": 0,
            "final_failures": 0
        }
    
    @property
    def _llm_type(self) -> str:
        return f"retryable_{self.base_llm._llm_type}"
    
    def _is_retryable_error(self, error: Exception) -> bool:
        """判断错误是否可重试"""
        return any(isinstance(error, err_type) for err_type in langchain_retryable_errors)
    
    def _calculate_delay(self, attempt: int, error: Exception) -> float:
        """计算重试延迟"""
        # 针对不同错误类型使用不同策略
        if isinstance(error, openai.error.RateLimitError):
            # 限流错误使用固定延迟
            delay = 60.0
        elif isinstance(error, (openai.error.Timeout, TimeoutError)):
            # 超时错误使用指数退避
            delay = self.base_delay * (self.backoff_factor ** (attempt - 1))
        else:
            # 其他错误使用线性退避
            delay = self.base_delay * attempt
        
        # 限制最大延迟
        delay = min(delay, self.max_delay)
        
        # 添加随机抖动
        if self.jitter:
            jitter_range = delay * 0.1
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    def _call(self,
              prompt: str,
              stop: Optional[List[str]] = None,
              run_manager: Optional[CallbackManagerForLLMRun] = None,
              **kwargs: Any) -> str:
        """同步调用with重试"""
        self.stats["total_requests"] += 1
        last_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                self.logger.info(f"LangChain LLM调用尝试 {attempt}/{self.max_retries}")
                
                # 调用基础LLM
                result = self.base_llm._call(
                    prompt=prompt,
                    stop=stop,
                    run_manager=run_manager,
                    **kwargs
                )
                
                # 成功统计
                if attempt > 1:
                    self.stats["success_after_retry"] += 1
                    self.logger.info(f"LLM调用重试成功，尝试次数: {attempt}")
                
                return result
                
            except Exception as error:
                last_error = error
                
                # 检查是否可重试
                if not self._is_retryable_error(error):
                    self.logger.error(f"不可重试的LLM错误: {error}")
                    self.stats["final_failures"] += 1
                    raise error
                
                # 最后一次尝试
                if attempt == self.max_retries:
                    self.logger.error(f"LLM调用重试次数已达上限: {error}")
                    self.stats["final_failures"] += 1
                    raise error
                
                # 计算延迟并等待
                delay = self._calculate_delay(attempt, error)
                
                if attempt == 1:
                    self.stats["retry_requests"] += 1
                
                self.logger.warning(
                    f"LLM调用失败 (尝试 {attempt}/{self.max_retries}): {error}, "
                    f"{delay:.2f}秒后重试"
                )
                
                time.sleep(delay)
        
        # 理论上不会到达这里
        raise last_error
    
    async def _acall(self,
                     prompt: str,
                     stop: Optional[List[str]] = None,
                     run_manager: Optional[CallbackManagerForLLMRun] = None,
                     **kwargs: Any) -> str:
        """异步调用with重试"""
        self.stats["total_requests"] += 1
        last_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                self.logger.info(f"异步LangChain LLM调用尝试 {attempt}/{self.max_retries}")
                
                # 调用基础LLM的异步方法
                if hasattr(self.base_llm, '_acall'):
                    result = await self.base_llm._acall(
                        prompt=prompt,
                        stop=stop,
                        run_manager=run_manager,
                        **kwargs
                    )
                else:
                    # 如果基础LLM不支持异步，使用同步方法
                    result = self.base_llm._call(
                        prompt=prompt,
                        stop=stop,
                        run_manager=run_manager,
                        **kwargs
                    )
                
                # 成功统计
                if attempt > 1:
                    self.stats["success_after_retry"] += 1
                    self.logger.info(f"异步LLM调用重试成功，尝试次数: {attempt}")
                
                return result
                
            except Exception as error:
                last_error = error
                
                # 检查是否可重试
                if not self._is_retryable_error(error):
                    self.logger.error(f"不可重试的异步LLM错误: {error}")
                    self.stats["final_failures"] += 1
                    raise error
                
                # 最后一次尝试
                if attempt == self.max_retries:
                    self.logger.error(f"异步LLM调用重试次数已达上限: {error}")
                    self.stats["final_failures"] += 1
                    raise error
                
                # 计算延迟并等待
                delay = self._calculate_delay(attempt, error)
                
                if attempt == 1:
                    self.stats["retry_requests"] += 1
                
                self.logger.warning(
                    f"异步LLM调用失败 (尝试 {attempt}/{self.max_retries}): {error}, "
                    f"{delay:.2f}秒后重试"
                )
                
                await asyncio.sleep(delay)
        
        # 理论上不会到达这里
        raise last_error
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total = self.stats["total_requests"]
        if total == 0:
            return self.stats
        
        return {
            **self.stats,
            "retry_rate": self.stats["retry_requests"] / total,
            "success_rate": (total - self.stats["final_failures"]) / total,
            "retry_success_rate": (
                self.stats["success_after_retry"] / self.stats["retry_requests"]
                if self.stats["retry_requests"] > 0 else 0
            )
        }
```

### 2.2 LangChain Chain重试包装器
```python
from langchain.chains.base import Chain
from langchain.schema import BaseMemory

class RetryableChain(Chain):
    """带重试机制的Chain包装器"""
    
    def __init__(self,
                 base_chain: Chain,
                 max_retries: int = 3,
                 base_delay: float = 2.0,
                 backoff_factor: float = 2.0):
        super().__init__()
        self.base_chain = base_chain
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.backoff_factor = backoff_factor
        self.logger = logging.getLogger(__name__)
    
    @property
    def input_keys(self) -> List[str]:
        return self.base_chain.input_keys
    
    @property
    def output_keys(self) -> List[str]:
        return self.base_chain.output_keys
    
    def _call(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行Chain with重试"""
        last_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                self.logger.info(f"Chain调用尝试 {attempt}/{self.max_retries}")
                
                # 调用基础Chain
                result = self.base_chain._call(inputs)
                
                if attempt > 1:
                    self.logger.info(f"Chain调用重试成功，尝试次数: {attempt}")
                
                return result
                
            except Exception as error:
                last_error = error
                
                # 检查是否可重试
                if not self._is_retryable_error(error):
                    self.logger.error(f"不可重试的Chain错误: {error}")
                    raise error
                
                # 最后一次尝试
                if attempt == self.max_retries:
                    self.logger.error(f"Chain调用重试次数已达上限: {error}")
                    raise error
                
                # 计算延迟并等待
                delay = self.base_delay * (self.backoff_factor ** (attempt - 1))
                
                self.logger.warning(
                    f"Chain调用失败 (尝试 {attempt}/{self.max_retries}): {error}, "
                    f"{delay:.2f}秒后重试"
                )
                
                time.sleep(delay)
        
        raise last_error
    
    def _is_retryable_error(self, error: Exception) -> bool:
        """判断Chain错误是否可重试"""
        return any(isinstance(error, err_type) for err_type in langchain_retryable_errors)
```

## 3. 实际使用示例

### 3.1 OpenAI LLM重试配置
```python
from langchain.llms import OpenAI
from langchain.chat_models import ChatOpenAI

# 创建基础LLM
base_llm = ChatOpenAI(
    model_name="gpt-3.5-turbo",
    temperature=0.7,
    max_tokens=4000,
    request_timeout=120,  # 基础超时时间
    max_retries=0,  # 禁用LangChain内置重试，使用我们的重试机制
)

# 包装为可重试LLM
retryable_llm = RetryableLLM(
    base_llm=base_llm,
    max_retries=3,
    base_delay=2.0,
    max_delay=60.0,
    backoff_factor=2.0,
    jitter=True
)

# 使用示例
async def generate_chapter_content(prompt: str) -> str:
    """生成章节内容"""
    try:
        result = await retryable_llm._acall(prompt)
        return result
    except Exception as e:
        logger.error(f"章节生成最终失败: {e}")
        raise
```

### 3.2 集成到Celery任务
```python
from celery import Celery
import asyncio

@celery_app.task(bind=True, max_retries=3)
def generate_chapter_with_langchain(self, user_id: int, novel_id: int, chapter_data: dict):
    """使用LangChain生成章节的Celery任务"""
    
    task_id = self.request.id
    
    try:
        # 更新任务状态
        update_task_progress(task_id, "processing", 10)
        
        # 构建prompt
        prompt = build_chapter_prompt(chapter_data)
        
        # 更新进度
        update_task_progress(task_id, "processing", 30)
        
        # 使用可重试LLM生成内容
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            content = loop.run_until_complete(
                retryable_llm._acall(prompt)
            )
        finally:
            loop.close()
        
        # 更新进度
        update_task_progress(task_id, "processing", 80)
        
        # 保存结果
        chapter_result = {
            "novel_id": novel_id,
            "chapter_number": chapter_data["chapter_number"],
            "title": chapter_data["title"],
            "content": content,
            "word_count": len(content),
            "generated_at": datetime.utcnow().isoformat()
        }
        
        save_chapter_to_db(chapter_result)
        
        # 完成
        update_task_progress(task_id, "completed", 100)
        
        # 推送结果
        send_progress_update(user_id, {
            "task_id": task_id,
            "status": "completed",
            "result": chapter_result
        })
        
        return chapter_result
        
    except Exception as e:
        # 错误处理
        error_info = {
            "task_id": task_id,
            "status": "failed",
            "error": str(e),
            "retry_count": self.request.retries
        }
        
        update_task_progress(task_id, "failed", 0, str(e))
        send_progress_update(user_id, error_info)
        
        # 如果是可重试错误且未达到最大重试次数，则重试
        if (self.request.retries < self.max_retries and 
            any(isinstance(e, err_type) for err_type in langchain_retryable_errors)):
            
            # 计算重试延迟
            delay = 2 ** self.request.retries
            
            logger.warning(f"Celery任务重试，延迟{delay}秒: {e}")
            raise self.retry(countdown=delay, exc=e)
        
        raise e

def build_chapter_prompt(chapter_data: dict) -> str:
    """构建章节生成prompt"""
    return f"""
请根据以下信息生成小说章节内容：

小说标题：{chapter_data.get('novel_title', '')}
章节标题：{chapter_data.get('title', '')}
章节序号：{chapter_data.get('chapter_number', 1)}
小说类型：{chapter_data.get('genre', '')}
写作风格：{chapter_data.get('style', '')}
字数要求：2000-3500字

{chapter_data.get('additional_context', '')}

请生成引人入胜的章节内容，保持情节连贯性和人物一致性。
"""
```

## 4. 监控和配置

### 4.1 LangChain重试监控
```python
class LangChainRetryMonitor:
    def __init__(self):
        self.metrics = {
            "llm_calls": 0,
            "llm_retries": 0,
            "llm_failures": 0,
            "chain_calls": 0,
            "chain_retries": 0,
            "chain_failures": 0,
            "error_types": {}
        }
    
    def record_llm_call(self, success: bool, retries: int, error_type: str = None):
        """记录LLM调用"""
        self.metrics["llm_calls"] += 1
        if retries > 0:
            self.metrics["llm_retries"] += retries
        if not success:
            self.metrics["llm_failures"] += 1
            if error_type:
                self.metrics["error_types"][error_type] = (
                    self.metrics["error_types"].get(error_type, 0) + 1
                )
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        total_calls = self.metrics["llm_calls"]
        if total_calls == 0:
            return self.metrics
        
        return {
            **self.metrics,
            "llm_retry_rate": self.metrics["llm_retries"] / total_calls,
            "llm_failure_rate": self.metrics["llm_failures"] / total_calls,
            "avg_retries_per_call": self.metrics["llm_retries"] / total_calls
        }
```

### 4.2 配置管理
```python
from pydantic import BaseSettings

class LangChainRetrySettings(BaseSettings):
    # LLM重试配置
    llm_max_retries: int = 3
    llm_base_delay: float = 2.0
    llm_max_delay: float = 60.0
    llm_backoff_factor: float = 2.0
    llm_jitter: bool = True
    
    # 超时配置
    llm_request_timeout: int = 120
    llm_total_timeout: int = 300
    
    # 特殊错误处理
    rate_limit_delay: int = 60
    rate_limit_max_retries: int = 5
    
    # 监控配置
    enable_retry_monitoring: bool = True
    log_retry_details: bool = True
    
    class Config:
        env_prefix = "LANGCHAIN_RETRY_"
        env_file = ".env"

# 全局配置
retry_settings = LangChainRetrySettings()
```

这个基于LangChain的弹性重试机制设计专门针对LangChain的错误类型和调用模式进行了优化，提供了完整的重试策略、监控和配置管理功能。
