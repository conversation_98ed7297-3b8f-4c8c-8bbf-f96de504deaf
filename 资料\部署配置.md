# AI小说生成Web应用部署配置

## 1. 开发环境配置

### 1.1 环境变量配置 (.env)
```bash
# 基础配置
PROJECT_NAME="AI Novel Generator"
VERSION="1.0.0"
DEBUG=true
API_V1_STR="/api/v1"

# 数据库配置
DATABASE_URL="mysql+pymysql://bookweb:password123@localhost:3306/bookweb"
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_ECHO=false

# Redis配置
REDIS_URL="redis://localhost:6379/0"
REDIS_PASSWORD=""
REDIS_MAX_CONNECTIONS=100

# JWT配置
SECRET_KEY="your-super-secret-key-change-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# AI服务配置
AI_SERVICE_URL="https://api.openai.com/v1"
AI_SERVICE_API_KEY="your-openai-api-key"
AI_MODEL_NAME="gpt-3.5-turbo"

# 限流配置
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000
RATE_LIMIT_PER_DAY=10000

# Celery配置
CELERY_BROKER_URL="redis://localhost:6379/2"
CELERY_RESULT_BACKEND="redis://localhost:6379/2"

# 文件上传配置
UPLOAD_DIR="uploads"
MAX_FILE_SIZE=10485760
```

### 1.2 Docker开发环境 (docker-compose.dev.yml)
```yaml
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: bookweb_mysql_dev
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: bookweb
      MYSQL_USER: bookweb
      MYSQL_PASSWORD: password123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - bookweb_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: bookweb_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - bookweb_network

  # FastAPI应用
  api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: bookweb_api_dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - ./uploads:/app/uploads
    environment:
      - DATABASE_URL=mysql+pymysql://bookweb:password123@mysql:3306/bookweb
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/2
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - mysql
      - redis
    networks:
      - bookweb_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: bookweb_celery_dev
    volumes:
      - .:/app
    environment:
      - DATABASE_URL=mysql+pymysql://bookweb:password123@mysql:3306/bookweb
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/2
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - mysql
      - redis
    networks:
      - bookweb_network
    command: celery -A app.tasks.celery_app worker --loglevel=info

volumes:
  mysql_data:
  redis_data:

networks:
  bookweb_network:
    driver: bridge
```

### 1.3 开发环境Dockerfile (Dockerfile.dev)
```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建上传目录
RUN mkdir -p uploads

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

## 2. 生产环境配置

### 2.1 生产环境变量 (.env.prod)
```bash
# 基础配置
PROJECT_NAME="AI Novel Generator"
VERSION="1.0.0"
DEBUG=false
API_V1_STR="/api/v1"

# 数据库配置
DATABASE_URL="mysql+pymysql://bookweb:SecurePassword123@mysql:3306/bookweb"
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=40
DATABASE_ECHO=false

# Redis配置
REDIS_URL="redis://redis:6379/0"
REDIS_PASSWORD="SecureRedisPassword123"
REDIS_MAX_CONNECTIONS=200

# JWT配置
SECRET_KEY="super-secure-secret-key-for-production-change-this"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# AI服务配置
AI_SERVICE_URL="https://api.openai.com/v1"
AI_SERVICE_API_KEY="your-production-openai-api-key"
AI_MODEL_NAME="gpt-4"

# 限流配置
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=5000
RATE_LIMIT_PER_DAY=50000

# Celery配置
CELERY_BROKER_URL="redis://redis:6379/2"
CELERY_RESULT_BACKEND="redis://redis:6379/2"

# 监控配置
SENTRY_DSN="your-sentry-dsn"
LOG_LEVEL="INFO"
```

### 2.2 生产环境Docker配置 (docker-compose.prod.yml)
```yaml
version: '3.8'

services:
  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: bookweb_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - api
    networks:
      - bookweb_network
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: bookweb_mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: bookweb
      MYSQL_USER: bookweb
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    networks:
      - bookweb_network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis集群
  redis:
    image: redis:7-alpine
    container_name: bookweb_redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - bookweb_network
    restart: unless-stopped

  # FastAPI应用
  api:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: bookweb_api
    environment:
      - DATABASE_URL=mysql+pymysql://bookweb:${MYSQL_PASSWORD}@mysql:3306/bookweb
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/2
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/2
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - bookweb_network
    restart: unless-stopped
    deploy:
      replicas: 2

  # Celery Worker
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile.prod
    environment:
      - DATABASE_URL=mysql+pymysql://bookweb:${MYSQL_PASSWORD}@mysql:3306/bookweb
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/2
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/2
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - bookweb_network
    restart: unless-stopped
    deploy:
      replicas: 2
    command: celery -A app.tasks.celery_app worker --loglevel=info --concurrency=4

  # Celery Beat调度器
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile.prod
    environment:
      - DATABASE_URL=mysql+pymysql://bookweb:${MYSQL_PASSWORD}@mysql:3306/bookweb
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/2
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/2
    volumes:
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - bookweb_network
    restart: unless-stopped
    command: celery -A app.tasks.celery_app beat --loglevel=info

volumes:
  mysql_data:
  redis_data:

networks:
  bookweb_network:
    driver: bridge
```

### 2.3 生产环境Dockerfile (Dockerfile.prod)
```dockerfile
FROM python:3.9-slim as builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 生产阶段
FROM python:3.9-slim

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    default-libmysqlclient-dev \
    && rm -rf /var/lib/apt/lists/*

# 从builder阶段复制Python包
COPY --from=builder /usr/local/lib/python3.9/site-packages /usr/local/lib/python3.9/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 设置工作目录
WORKDIR /app

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p uploads logs && \
    chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

### 2.4 Nginx配置 (nginx/nginx.conf)
```nginx
events {
    worker_connections 1024;
}

http {
    upstream api_backend {
        server api:8000;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    server {
        listen 80;
        server_name your-domain.com;
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

        # 静态文件
        location /uploads/ {
            alias /var/www/uploads/;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        # API接口
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 登录接口特殊限流
        location /api/v1/auth/login {
            limit_req zone=login burst=5 nodelay;
            
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 健康检查
        location /health {
            proxy_pass http://api_backend;
            access_log off;
        }
    }
}
```

## 3. 部署脚本

### 3.1 部署脚本 (scripts/deploy.sh)
```bash
#!/bin/bash

set -e

echo "开始部署AI小说生成应用..."

# 检查环境变量
if [ ! -f .env.prod ]; then
    echo "错误: .env.prod 文件不存在"
    exit 1
fi

# 拉取最新代码
echo "拉取最新代码..."
git pull origin main

# 构建Docker镜像
echo "构建Docker镜像..."
docker-compose -f docker-compose.prod.yml build --no-cache

# 停止旧服务
echo "停止旧服务..."
docker-compose -f docker-compose.prod.yml down

# 数据库迁移
echo "执行数据库迁移..."
docker-compose -f docker-compose.prod.yml run --rm api alembic upgrade head

# 启动新服务
echo "启动新服务..."
docker-compose -f docker-compose.prod.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 健康检查
echo "执行健康检查..."
if curl -f http://localhost/health; then
    echo "部署成功!"
else
    echo "部署失败，回滚..."
    docker-compose -f docker-compose.prod.yml down
    exit 1
fi

echo "部署完成!"
```

### 3.2 数据库初始化脚本 (scripts/init_db.py)
```python
#!/usr/bin/env python3

import asyncio
from sqlalchemy import create_engine
from app.config import settings
from app.models.base import Base
from app.models import user, novel, generation
from app.services.auth_service import auth_service

def init_database():
    """初始化数据库"""
    engine = create_engine(settings.DATABASE_URL)
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成")
    
    # 创建默认管理员用户
    from sqlalchemy.orm import sessionmaker
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        admin_data = {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "admin123",
            "nickname": "系统管理员"
        }
        
        admin_user = auth_service.register_user(db, admin_data)
        print(f"管理员用户创建完成: {admin_user.username}")
        
    except Exception as e:
        print(f"创建管理员用户失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
```

## 4. 监控配置

### 4.1 健康检查端点
```python
# app/api/v1/health.py
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.redis_client import redis_client

router = APIRouter()

@router.get("/health")
async def health_check(db: Session = Depends(get_db)):
    """健康检查"""
    try:
        # 检查数据库连接
        db.execute("SELECT 1")
        
        # 检查Redis连接
        redis_client.ping()
        
        return {
            "status": "healthy",
            "database": "connected",
            "redis": "connected"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
```

这个部署配置提供了完整的开发和生产环境部署方案，包括Docker容器化、Nginx反向代理、SSL配置、监控健康检查等，确保应用能够稳定高效地运行。
