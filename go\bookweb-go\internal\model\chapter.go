package model

import (
	"time"
	"gorm.io/gorm"
)

type Chapter struct {
	ID             uint           `json:"id" gorm:"primaryKey"`
	NovelID        uint           `json:"novel_id" gorm:"not null;index"`
	ChapterNumber  int            `json:"chapter_number" gorm:"not null"`
	Title          string         `json:"title" gorm:"size:200;not null"`
	Content        string         `json:"content" gorm:"type:longtext"`
	WordCount      int            `json:"word_count" gorm:"default:0"`
	Status         int            `json:"status" gorm:"default:0"` // 0:生成中 1:完成 2:失败
	GenerationTime int            `json:"generation_time"`         // 生成耗时(秒)
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Novel Novel `json:"novel,omitempty" gorm:"foreignKey:NovelID"`
}

func (Chapter) TableName() string {
	return "chapters"
}

// BeforeCreate 创建前的钩子
func (c *Chapter) BeforeCreate(tx *gorm.DB) error {
	// 可以在这里添加创建前的逻辑
	return nil
}
