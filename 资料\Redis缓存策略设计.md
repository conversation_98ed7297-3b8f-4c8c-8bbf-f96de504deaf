# AI小说生成Web应用Redis缓存策略设计

## 1. Redis架构设计

### 1.1 Redis部署架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Redis Cluster                           │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Master    │  │   Master    │  │   Master    │        │
│  │   Node 1    │  │   Node 2    │  │   Node 3    │        │
│  │             │  │             │  │             │        │
│  │ Slots:      │  │ Slots:      │  │ Slots:      │        │
│  │ 0-5460      │  │ 5461-10922  │  │ 10923-16383 │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│         │                 │                 │              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Slave     │  │   Slave     │  │   Slave     │        │
│  │   Node 1    │  │   Node 2    │  │   Node 3    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 数据库分配策略
- **DB 0**: 用户会话和认证相关
- **DB 1**: 业务数据缓存
- **DB 2**: 任务队列和消息
- **DB 3**: 统计和监控数据
- **DB 4**: 临时数据和锁

## 2. 缓存数据结构设计

### 2.1 用户会话管理 (DB 0)

#### 用户登录会话
```python
# Key格式: session:user:{user_id}
# 数据类型: Hash
# TTL: 24小时

session:user:12345 = {
    "user_id": "12345",
    "username": "john_doe",
    "email": "<EMAIL>",
    "role": "user",
    "permissions": ["read", "write"],
    "login_time": "2024-01-01T10:00:00Z",
    "last_active": "2024-01-01T15:30:00Z",
    "ip_address": "*************",
    "device_info": "Chrome/120.0 Windows"
}
```

#### JWT Token黑名单
```python
# Key格式: blacklist:token:{token_jti}
# 数据类型: String
# TTL: Token过期时间

blacklist:token:abc123 = "revoked"
```

#### 用户在线状态
```python
# Key格式: online:users
# 数据类型: Set
# TTL: 无(定期清理)

online:users = {12345, 12346, 12347, ...}
```

### 2.2 API限流控制 (DB 0)

#### 用户级别限流
```python
# Key格式: rate_limit:user:{user_id}:{window}
# 数据类型: String (计数器)
# TTL: 时间窗口大小

rate_limit:user:12345:minute = "45"  # TTL: 60秒
rate_limit:user:12345:hour = "850"   # TTL: 3600秒
rate_limit:user:12345:day = "5000"   # TTL: 86400秒
```

#### IP级别限流
```python
# Key格式: rate_limit:ip:{ip_address}:{window}
# 数据类型: String (计数器)
# TTL: 时间窗口大小

rate_limit:ip:*************:minute = "100"
```

#### 接口级别限流
```python
# Key格式: rate_limit:api:{endpoint}:{window}
# 数据类型: String (计数器)
# TTL: 时间窗口大小

rate_limit:api:/api/v1/generate:minute = "500"
```

### 2.3 业务数据缓存 (DB 1)

#### 用户信息缓存
```python
# Key格式: cache:user:{user_id}
# 数据类型: Hash
# TTL: 30分钟

cache:user:12345 = {
    "id": "12345",
    "username": "john_doe",
    "nickname": "John",
    "avatar_url": "https://example.com/avatar.jpg",
    "status": "1",
    "created_at": "2024-01-01T00:00:00Z"
}
```

#### 小说信息缓存
```python
# Key格式: cache:novel:{novel_id}
# 数据类型: Hash
# TTL: 1小时

cache:novel:67890 = {
    "id": "67890",
    "title": "AI生成的小说",
    "author_id": "12345",
    "genre": "科幻",
    "status": "1",
    "word_count": "50000",
    "chapter_count": "20",
    "updated_at": "2024-01-01T12:00:00Z"
}
```

#### 章节内容缓存
```python
# Key格式: cache:chapter:{chapter_id}
# 数据类型: Hash
# TTL: 2小时

cache:chapter:11111 = {
    "id": "11111",
    "novel_id": "67890",
    "title": "第一章 开始",
    "content": "章节内容...",
    "word_count": "2500"
}
```

#### 热门内容缓存
```python
# Key格式: cache:hot:novels:{genre}
# 数据类型: Sorted Set (按热度排序)
# TTL: 15分钟

cache:hot:novels:科幻 = {
    67890: 9.5,  # novel_id: score
    67891: 9.2,
    67892: 8.8
}
```

### 2.4 任务队列管理 (DB 2)

#### 生成任务队列
```python
# Key格式: queue:generation:{priority}
# 数据类型: List (FIFO队列)
# TTL: 无

queue:generation:high = [task_id_1, task_id_2, ...]
queue:generation:normal = [task_id_3, task_id_4, ...]
queue:generation:low = [task_id_5, task_id_6, ...]
```

#### 任务状态缓存
```python
# Key格式: task:status:{task_id}
# 数据类型: Hash
# TTL: 24小时

task:status:task_123 = {
    "task_id": "task_123",
    "user_id": "12345",
    "status": "processing",
    "progress": "45",
    "started_at": "2024-01-01T14:00:00Z",
    "estimated_completion": "2024-01-01T14:10:00Z"
}
```

#### 任务结果缓存
```python
# Key格式: task:result:{task_id}
# 数据类型: Hash
# TTL: 2小时

task:result:task_123 = {
    "task_id": "task_123",
    "status": "completed",
    "result_text": "生成的小说内容...",
    "word_count": "3000",
    "completed_at": "2024-01-01T14:08:30Z"
}
```

#### 消息通知队列
```python
# Key格式: queue:notifications:{user_id}
# 数据类型: List
# TTL: 7天

queue:notifications:12345 = [
    '{"type": "task_completed", "task_id": "task_123", "timestamp": "2024-01-01T14:08:30Z"}',
    '{"type": "system_notice", "message": "系统维护通知", "timestamp": "2024-01-01T10:00:00Z"}'
]
```

### 2.5 统计监控数据 (DB 3)

#### 实时统计数据
```python
# Key格式: stats:realtime:{metric}:{timestamp}
# 数据类型: Hash
# TTL: 24小时

stats:realtime:api_calls:2024010114 = {
    "total": "1500",
    "success": "1450",
    "error": "50",
    "avg_response_time": "120"
}
```

#### 用户行为统计
```python
# Key格式: stats:user:{user_id}:{date}
# 数据类型: Hash
# TTL: 30天

stats:user:12345:20240101 = {
    "login_count": "3",
    "api_calls": "50",
    "generation_tasks": "5",
    "words_generated": "15000"
}
```

#### 系统性能指标
```python
# Key格式: metrics:system:{timestamp}
# 数据类型: Hash
# TTL: 7天

metrics:system:2024010114 = {
    "cpu_usage": "65.5",
    "memory_usage": "78.2",
    "disk_usage": "45.0",
    "active_connections": "1200"
}
```

### 2.6 分布式锁 (DB 4)

#### 用户操作锁
```python
# Key格式: lock:user:{user_id}:{operation}
# 数据类型: String
# TTL: 30秒

lock:user:12345:generation = "locked_by_process_123"
```

#### 资源访问锁
```python
# Key格式: lock:resource:{resource_type}:{resource_id}
# 数据类型: String
# TTL: 60秒

lock:resource:novel:67890 = "editing_by_user_12345"
```

## 3. 缓存策略设计

### 3.1 缓存更新策略

#### Cache-Aside模式
```python
def get_user_info(user_id):
    # 1. 先查缓存
    cache_key = f"cache:user:{user_id}"
    user_info = redis.hgetall(cache_key)
    
    if user_info:
        return user_info
    
    # 2. 缓存未命中，查数据库
    user_info = db.query_user(user_id)
    
    if user_info:
        # 3. 更新缓存
        redis.hmset(cache_key, user_info)
        redis.expire(cache_key, 1800)  # 30分钟
    
    return user_info
```

#### Write-Through模式
```python
def update_user_info(user_id, user_data):
    # 1. 更新数据库
    db.update_user(user_id, user_data)
    
    # 2. 同时更新缓存
    cache_key = f"cache:user:{user_id}"
    redis.hmset(cache_key, user_data)
    redis.expire(cache_key, 1800)
```

### 3.2 缓存失效策略

#### TTL过期策略
```python
# 不同类型数据的TTL设置
TTL_CONFIG = {
    "user_session": 86400,      # 24小时
    "user_info": 1800,          # 30分钟
    "novel_info": 3600,         # 1小时
    "chapter_content": 7200,    # 2小时
    "hot_content": 900,         # 15分钟
    "task_result": 7200,        # 2小时
    "rate_limit": 3600,         # 1小时
}
```

#### 主动失效策略
```python
def invalidate_user_cache(user_id):
    """用户信息更新时主动失效相关缓存"""
    patterns = [
        f"cache:user:{user_id}",
        f"session:user:{user_id}",
        f"stats:user:{user_id}:*"
    ]
    
    for pattern in patterns:
        keys = redis.keys(pattern)
        if keys:
            redis.delete(*keys)
```

### 3.3 缓存预热策略

#### 应用启动预热
```python
def warm_up_cache():
    """应用启动时预热热点数据"""
    # 1. 预热热门小说
    hot_novels = db.get_hot_novels(limit=100)
    for novel in hot_novels:
        cache_key = f"cache:novel:{novel.id}"
        redis.hmset(cache_key, novel.to_dict())
        redis.expire(cache_key, 3600)
    
    # 2. 预热系统配置
    configs = db.get_system_configs()
    for config in configs:
        cache_key = f"config:{config.key}"
        redis.set(cache_key, config.value, ex=7200)
```

#### 定时预热任务
```python
@celery.task
def scheduled_cache_warm_up():
    """定时预热任务"""
    # 每小时执行一次，预热热点数据
    warm_up_hot_content()
    warm_up_user_stats()
    warm_up_system_metrics()
```

## 4. 性能优化策略

### 4.1 连接池配置
```python
REDIS_CONFIG = {
    "host": "localhost",
    "port": 6379,
    "password": "your_password",
    "max_connections": 100,
    "retry_on_timeout": True,
    "socket_keepalive": True,
    "socket_keepalive_options": {},
    "health_check_interval": 30
}
```

### 4.2 批量操作优化
```python
def batch_cache_user_info(user_ids):
    """批量缓存用户信息"""
    pipe = redis.pipeline()
    
    for user_id in user_ids:
        user_info = db.get_user(user_id)
        if user_info:
            cache_key = f"cache:user:{user_id}"
            pipe.hmset(cache_key, user_info.to_dict())
            pipe.expire(cache_key, 1800)
    
    pipe.execute()
```

### 4.3 内存优化
```python
# Redis内存优化配置
REDIS_MEMORY_CONFIG = {
    "maxmemory": "2gb",
    "maxmemory-policy": "allkeys-lru",
    "hash-max-ziplist-entries": 512,
    "hash-max-ziplist-value": 64,
    "list-max-ziplist-size": -2,
    "set-max-intset-entries": 512
}
```

## 5. 监控和运维

### 5.1 关键指标监控
- 缓存命中率
- 内存使用率
- 连接数
- 操作延迟
- 错误率

### 5.2 告警策略
- 缓存命中率 < 80%
- 内存使用率 > 85%
- 连接数 > 90%
- 平均延迟 > 10ms
- 错误率 > 1%

这个Redis缓存策略设计提供了完整的缓存解决方案，涵盖了用户会话、业务数据、任务队列、统计监控等各个方面，能够有效提升系统性能和用户体验。
