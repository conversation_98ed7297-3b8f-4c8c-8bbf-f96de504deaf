# AI请求弹性重试机制设计
## 第三方API调用的超时处理和重试策略

## 1. 重试机制设计原则

### 1.1 重试策略类型
```yaml
重试策略分类:
  固定间隔重试:
    - 适用场景: 网络抖动、临时故障
    - 重试间隔: 固定时间间隔
    - 优点: 简单易实现
    - 缺点: 可能加重服务器负担

  指数退避重试:
    - 适用场景: 服务过载、限流
    - 重试间隔: 指数级增长
    - 优点: 减少服务器压力
    - 缺点: 重试时间较长

  线性退避重试:
    - 适用场景: 一般性故障
    - 重试间隔: 线性增长
    - 优点: 平衡重试速度和服务器压力
    - 缺点: 需要调优参数

  随机抖动重试:
    - 适用场景: 避免惊群效应
    - 重试间隔: 在基础间隔上加随机值
    - 优点: 分散重试请求
    - 缺点: 增加复杂度
```

### 1.2 错误分类处理
```yaml
可重试错误:
  - 网络超时 (Timeout)
  - 连接错误 (Connection Error)
  - 服务器内部错误 (5xx)
  - 限流错误 (429 Too Many Requests)
  - 临时不可用 (503 Service Unavailable)

不可重试错误:
  - 认证错误 (401 Unauthorized)
  - 权限错误 (403 Forbidden)
  - 请求格式错误 (400 Bad Request)
  - 资源不存在 (404 Not Found)
  - 请求实体过大 (413 Payload Too Large)

特殊处理错误:
  - 部分成功 (206 Partial Content)
  - 重定向 (3xx) - 需要更新URL
  - 配额超限 - 需要等待重置时间
```

## 2. 弹性重试机制实现

### 2.1 核心重试类设计
```python
import asyncio
import random
import time
import logging
from typing import Optional, Callable, Any, Dict, List
from enum import Enum
from dataclasses import dataclass
import aiohttp

class RetryStrategy(Enum):
    FIXED = "fixed"
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    CUSTOM = "custom"

@dataclass
class RetryConfig:
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    backoff_factor: float = 2.0
    jitter: bool = True
    timeout: float = 120.0
    
class RetryableError(Exception):
    """可重试的错误"""
    pass

class NonRetryableError(Exception):
    """不可重试的错误"""
    pass

class AIRequestRetryHandler:
    def __init__(self, config: RetryConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    def calculate_delay(self, attempt: int) -> float:
        """计算重试延迟时间"""
        if self.config.strategy == RetryStrategy.FIXED:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.EXPONENTIAL:
            delay = self.config.base_delay * (self.config.backoff_factor ** (attempt - 1))
        elif self.config.strategy == RetryStrategy.LINEAR:
            delay = self.config.base_delay * attempt
        else:
            delay = self.config.base_delay
        
        # 限制最大延迟时间
        delay = min(delay, self.config.max_delay)
        
        # 添加随机抖动
        if self.config.jitter:
            jitter_range = delay * 0.1  # 10%的抖动
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    def is_retryable_error(self, error: Exception, response: Optional[aiohttp.ClientResponse] = None) -> bool:
        """判断错误是否可重试"""
        # 网络相关错误
        if isinstance(error, (aiohttp.ClientTimeout, aiohttp.ClientConnectionError, 
                             aiohttp.ClientConnectorError, asyncio.TimeoutError)):
            return True
        
        # HTTP响应错误
        if response:
            status = response.status
            # 5xx服务器错误通常可重试
            if 500 <= status < 600:
                return True
            # 429限流错误可重试
            if status == 429:
                return True
            # 408请求超时可重试
            if status == 408:
                return True
            # 502, 503, 504网关错误可重试
            if status in [502, 503, 504]:
                return True
        
        return False
    
    async def execute_with_retry(self, 
                                func: Callable,
                                *args,
                                **kwargs) -> Any:
        """执行带重试的函数"""
        last_error = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                self.logger.info(f"AI API调用尝试 {attempt}/{self.config.max_attempts}")
                
                # 执行函数
                result = await func(*args, **kwargs)
                
                self.logger.info(f"AI API调用成功，尝试次数: {attempt}")
                return result
                
            except Exception as error:
                last_error = error
                
                # 判断是否可重试
                response = getattr(error, 'response', None)
                if not self.is_retryable_error(error, response):
                    self.logger.error(f"不可重试错误: {error}")
                    raise NonRetryableError(f"不可重试错误: {error}") from error
                
                # 如果是最后一次尝试，直接抛出错误
                if attempt == self.config.max_attempts:
                    self.logger.error(f"重试次数已达上限，最终错误: {error}")
                    raise RetryableError(f"重试失败，最终错误: {error}") from error
                
                # 计算延迟时间
                delay = self.calculate_delay(attempt)
                
                self.logger.warning(
                    f"AI API调用失败 (尝试 {attempt}/{self.config.max_attempts}): {error}, "
                    f"{delay:.2f}秒后重试"
                )
                
                # 等待后重试
                await asyncio.sleep(delay)
        
        # 理论上不会到达这里
        raise RetryableError(f"重试失败: {last_error}")
```

### 2.2 AI API调用管理器增强版
```python
class EnhancedAIAPIManager:
    def __init__(self):
        self.api_endpoints = [
            {
                "name": "primary",
                "url": "https://api1.example.com/generate",
                "weight": 3,
                "timeout": 120,
                "retry_config": RetryConfig(
                    max_attempts=3,
                    base_delay=2.0,
                    strategy=RetryStrategy.EXPONENTIAL,
                    backoff_factor=2.0
                )
            },
            {
                "name": "secondary", 
                "url": "https://api2.example.com/generate",
                "weight": 2,
                "timeout": 150,
                "retry_config": RetryConfig(
                    max_attempts=2,
                    base_delay=3.0,
                    strategy=RetryStrategy.LINEAR
                )
            },
            {
                "name": "backup",
                "url": "https://api3.example.com/generate", 
                "weight": 1,
                "timeout": 180,
                "retry_config": RetryConfig(
                    max_attempts=2,
                    base_delay=5.0,
                    strategy=RetryStrategy.FIXED
                )
            }
        ]
        
        self.concurrent_limit = 50
        self.semaphore = asyncio.Semaphore(self.concurrent_limit)
        self.session = None
        self.circuit_breaker = CircuitBreaker()
        
    async def init_session(self):
        """初始化HTTP会话"""
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=20,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(
            total=200,  # 总超时时间
            connect=10,  # 连接超时
            sock_read=180  # 读取超时
        )
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                "User-Agent": "AI-Novel-Generator/1.0",
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
        )
    
    async def call_single_endpoint(self, 
                                  endpoint: Dict,
                                  prompt: str,
                                  chapter_info: Dict) -> Dict:
        """调用单个API端点"""
        retry_handler = AIRequestRetryHandler(endpoint["retry_config"])
        
        async def _make_request():
            payload = {
                "prompt": prompt,
                "max_tokens": 4000,
                "temperature": 0.7,
                "chapter_info": chapter_info,
                "timeout": endpoint["timeout"]
            }
            
            async with self.session.post(
                endpoint["url"],
                json=payload,
                timeout=endpoint["timeout"]
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    return {
                        "success": True,
                        "content": result.get("content", ""),
                        "word_count": len(result.get("content", "")),
                        "endpoint": endpoint["name"],
                        "attempts": 1
                    }
                else:
                    # 创建带响应信息的错误
                    error = aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=f"API返回错误状态: {response.status}"
                    )
                    error.response = response
                    raise error
        
        try:
            return await retry_handler.execute_with_retry(_make_request)
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "endpoint": endpoint["name"]
            }
    
    async def generate_chapter_with_fallback(self,
                                           prompt: str,
                                           chapter_info: Dict) -> Dict:
        """带故障转移的章节生成"""
        async with self.semaphore:
            start_time = time.time()
            
            # 按权重排序端点
            sorted_endpoints = sorted(
                self.api_endpoints,
                key=lambda x: x["weight"],
                reverse=True
            )
            
            last_error = None
            
            for endpoint in sorted_endpoints:
                # 检查熔断器状态
                if not self.circuit_breaker.can_execute(endpoint["name"]):
                    self.logger.warning(f"端点 {endpoint['name']} 熔断器开启，跳过")
                    continue
                
                try:
                    self.logger.info(f"尝试调用端点: {endpoint['name']}")
                    
                    result = await self.call_single_endpoint(
                        endpoint, prompt, chapter_info
                    )
                    
                    if result["success"]:
                        # 记录成功
                        self.circuit_breaker.record_success(endpoint["name"])
                        
                        result.update({
                            "generation_time": time.time() - start_time,
                            "fallback_used": endpoint != sorted_endpoints[0]
                        })
                        
                        self.logger.info(
                            f"章节生成成功，使用端点: {endpoint['name']}, "
                            f"耗时: {result['generation_time']:.2f}秒"
                        )
                        
                        return result
                    else:
                        # 记录失败
                        self.circuit_breaker.record_failure(endpoint["name"])
                        last_error = result["error"]
                        
                        self.logger.warning(
                            f"端点 {endpoint['name']} 调用失败: {result['error']}"
                        )
                        
                except Exception as e:
                    self.circuit_breaker.record_failure(endpoint["name"])
                    last_error = str(e)
                    
                    self.logger.error(
                        f"端点 {endpoint['name']} 调用异常: {e}"
                    )
            
            # 所有端点都失败
            return {
                "success": False,
                "error": f"所有API端点都不可用，最后错误: {last_error}",
                "generation_time": time.time() - start_time,
                "all_endpoints_failed": True
            }
```

### 2.3 熔断器实现
```python
import time
from enum import Enum
from typing import Dict

class CircuitState(Enum):
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态

class CircuitBreaker:
    def __init__(self,
                 failure_threshold: int = 5,
                 recovery_timeout: int = 60,
                 expected_exception: Exception = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        # 每个端点的状态
        self.states: Dict[str, CircuitState] = {}
        self.failure_counts: Dict[str, int] = {}
        self.last_failure_times: Dict[str, float] = {}
        self.success_counts: Dict[str, int] = {}
    
    def _get_state(self, endpoint: str) -> CircuitState:
        return self.states.get(endpoint, CircuitState.CLOSED)
    
    def _set_state(self, endpoint: str, state: CircuitState):
        self.states[endpoint] = state
        
        if state == CircuitState.OPEN:
            self.last_failure_times[endpoint] = time.time()
        elif state == CircuitState.CLOSED:
            self.failure_counts[endpoint] = 0
            self.success_counts[endpoint] = 0
    
    def can_execute(self, endpoint: str) -> bool:
        """检查是否可以执行请求"""
        state = self._get_state(endpoint)
        
        if state == CircuitState.CLOSED:
            return True
        elif state == CircuitState.OPEN:
            # 检查是否可以进入半开状态
            if (time.time() - self.last_failure_times.get(endpoint, 0) 
                >= self.recovery_timeout):
                self._set_state(endpoint, CircuitState.HALF_OPEN)
                return True
            return False
        elif state == CircuitState.HALF_OPEN:
            return True
        
        return False
    
    def record_success(self, endpoint: str):
        """记录成功调用"""
        state = self._get_state(endpoint)
        
        if state == CircuitState.HALF_OPEN:
            self.success_counts[endpoint] = self.success_counts.get(endpoint, 0) + 1
            # 连续成功一定次数后关闭熔断器
            if self.success_counts[endpoint] >= 2:
                self._set_state(endpoint, CircuitState.CLOSED)
        elif state == CircuitState.CLOSED:
            # 重置失败计数
            self.failure_counts[endpoint] = 0
    
    def record_failure(self, endpoint: str):
        """记录失败调用"""
        state = self._get_state(endpoint)
        
        self.failure_counts[endpoint] = self.failure_counts.get(endpoint, 0) + 1
        
        if (state in [CircuitState.CLOSED, CircuitState.HALF_OPEN] and
            self.failure_counts[endpoint] >= self.failure_threshold):
            self._set_state(endpoint, CircuitState.OPEN)
    
    def get_stats(self, endpoint: str) -> Dict:
        """获取端点统计信息"""
        return {
            "state": self._get_state(endpoint).value,
            "failure_count": self.failure_counts.get(endpoint, 0),
            "success_count": self.success_counts.get(endpoint, 0),
            "last_failure_time": self.last_failure_times.get(endpoint, 0)
        }
```

## 3. 超时配置策略

### 3.1 多层超时设计
```python
class TimeoutConfig:
    def __init__(self):
        self.timeouts = {
            # 连接超时
            "connect_timeout": 10,
            
            # 读取超时 (AI生成时间)
            "read_timeout": 120,
            
            # 总超时时间
            "total_timeout": 150,
            
            # 任务级超时 (Celery任务)
            "task_timeout": 300,
            
            # 用户级超时 (前端显示)
            "user_timeout": 180
        }
    
    def get_aiohttp_timeout(self) -> aiohttp.ClientTimeout:
        """获取aiohttp超时配置"""
        return aiohttp.ClientTimeout(
            total=self.timeouts["total_timeout"],
            connect=self.timeouts["connect_timeout"],
            sock_read=self.timeouts["read_timeout"]
        )
```

### 3.2 动态超时调整
```python
class DynamicTimeoutManager:
    def __init__(self):
        self.endpoint_stats = {}
        self.base_timeout = 120
        self.min_timeout = 60
        self.max_timeout = 300
    
    def update_stats(self, endpoint: str, response_time: float, success: bool):
        """更新端点统计信息"""
        if endpoint not in self.endpoint_stats:
            self.endpoint_stats[endpoint] = {
                "avg_response_time": response_time,
                "success_rate": 1.0 if success else 0.0,
                "request_count": 1
            }
        else:
            stats = self.endpoint_stats[endpoint]
            count = stats["request_count"]
            
            # 更新平均响应时间
            stats["avg_response_time"] = (
                (stats["avg_response_time"] * count + response_time) / (count + 1)
            )
            
            # 更新成功率
            stats["success_rate"] = (
                (stats["success_rate"] * count + (1.0 if success else 0.0)) / (count + 1)
            )
            
            stats["request_count"] = count + 1
    
    def get_dynamic_timeout(self, endpoint: str) -> int:
        """获取动态超时时间"""
        if endpoint not in self.endpoint_stats:
            return self.base_timeout
        
        stats = self.endpoint_stats[endpoint]
        avg_time = stats["avg_response_time"]
        success_rate = stats["success_rate"]
        
        # 基于平均响应时间和成功率调整超时
        # 响应时间越长，超时时间越长
        # 成功率越低，超时时间越短（快速失败）
        adjusted_timeout = avg_time * 1.5 * success_rate + self.base_timeout * (1 - success_rate)
        
        return max(self.min_timeout, min(self.max_timeout, int(adjusted_timeout)))
```

## 4. 监控和告警

### 4.1 重试指标监控
```python
class RetryMetrics:
    def __init__(self):
        self.metrics = {
            "total_requests": 0,
            "retry_requests": 0,
            "success_after_retry": 0,
            "final_failures": 0,
            "endpoint_stats": {},
            "error_types": {}
        }
    
    def record_request(self, endpoint: str, attempts: int, success: bool, error_type: str = None):
        """记录请求指标"""
        self.metrics["total_requests"] += 1
        
        if attempts > 1:
            self.metrics["retry_requests"] += 1
            if success:
                self.metrics["success_after_retry"] += 1
        
        if not success:
            self.metrics["final_failures"] += 1
            if error_type:
                self.metrics["error_types"][error_type] = (
                    self.metrics["error_types"].get(error_type, 0) + 1
                )
        
        # 端点统计
        if endpoint not in self.metrics["endpoint_stats"]:
            self.metrics["endpoint_stats"][endpoint] = {
                "requests": 0,
                "successes": 0,
                "retries": 0
            }
        
        stats = self.metrics["endpoint_stats"][endpoint]
        stats["requests"] += 1
        if success:
            stats["successes"] += 1
        if attempts > 1:
            stats["retries"] += 1
    
    def get_retry_rate(self) -> float:
        """获取重试率"""
        if self.metrics["total_requests"] == 0:
            return 0.0
        return self.metrics["retry_requests"] / self.metrics["total_requests"]
    
    def get_success_rate_after_retry(self) -> float:
        """获取重试后成功率"""
        if self.metrics["retry_requests"] == 0:
            return 0.0
        return self.metrics["success_after_retry"] / self.metrics["retry_requests"]
```

### 4.2 告警规则
```yaml
告警规则:
  重试率告警:
    - 条件: 重试率 > 30%
    - 级别: Warning
    - 描述: AI API重试率过高
  
  失败率告警:
    - 条件: 最终失败率 > 10%
    - 级别: Critical
    - 描述: AI API最终失败率过高
  
  响应时间告警:
    - 条件: 平均响应时间 > 150秒
    - 级别: Warning
    - 描述: AI API响应时间过长
  
  熔断器告警:
    - 条件: 熔断器开启
    - 级别: Critical
    - 描述: AI API端点熔断器开启
```

这个弹性重试机制设计提供了完整的超时处理和重试策略，包括多种重试算法、熔断器保护、动态超时调整和完善的监控告警，能够有效提高系统的稳定性和可用性。
