package repository

import (
	"gorm.io/gorm"
	"bookweb/internal/model"
)

type ChapterRepository struct {
	db *gorm.DB
}

func NewChapterRepository(db *gorm.DB) *ChapterRepository {
	return &ChapterRepository{db: db}
}

func (r *ChapterRepository) Create(chapter *model.Chapter) (*model.Chapter, error) {
	if err := r.db.Create(chapter).Error; err != nil {
		return nil, err
	}
	return chapter, nil
}

func (r *ChapterRepository) GetByID(id uint) (*model.Chapter, error) {
	var chapter model.Chapter
	if err := r.db.Preload("Novel").First(&chapter, id).Error; err != nil {
		return nil, err
	}
	return &chapter, nil
}

func (r *ChapterRepository) GetByNovelID(novelID uint, page, size int) ([]model.Chapter, int, error) {
	var chapters []model.Chapter
	var total int64

	offset := (page - 1) * size

	// 获取总数
	if err := r.db.Model(&model.Chapter{}).Where("novel_id = ?", novelID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := r.db.Where("novel_id = ?", novelID).
		Order("chapter_number ASC").
		Offset(offset).
		Limit(size).
		Find(&chapters).Error; err != nil {
		return nil, 0, err
	}

	return chapters, int(total), nil
}

func (r *ChapterRepository) Update(id uint, data map[string]interface{}) error {
	return r.db.Model(&model.Chapter{}).Where("id = ?", id).Updates(data).Error
}

func (r *ChapterRepository) UpdateStatus(id uint, status int) error {
	return r.db.Model(&model.Chapter{}).Where("id = ?", id).Update("status", status).Error
}

func (r *ChapterRepository) Delete(id uint) error {
	return r.db.Delete(&model.Chapter{}, id).Error
}
