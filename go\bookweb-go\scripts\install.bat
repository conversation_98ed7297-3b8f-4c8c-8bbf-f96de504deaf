@echo off
echo Installing BookWeb Go dependencies...

REM Check if Go is installed
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go is not installed or not in PATH
    echo Please install Go from https://golang.org/dl/
    pause
    exit /b 1
)

echo Go is installed, proceeding with dependency installation...

REM Initialize Go module if not exists
if not exist go.mod (
    echo Initializing Go module...
    go mod init bookweb
)

REM Download dependencies
echo Downloading dependencies...
go mod download
go mod tidy

echo Dependencies installed successfully!
echo.
echo To run the application:
echo   1. Configure configs/config.yaml with your database and AI API settings
echo   2. Run: make run
echo   or: go run cmd/api/main.go
echo.
pause
