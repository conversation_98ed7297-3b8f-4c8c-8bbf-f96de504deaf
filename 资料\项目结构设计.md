# AI小说生成Web应用项目结构设计

## 1. 项目目录结构

```
BookWeb/
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖包
├── pyproject.toml              # 项目配置文件
├── docker-compose.yml          # Docker编排文件
├── Dockerfile                  # Docker镜像构建文件
├── .env.example               # 环境变量示例
├── .gitignore                 # Git忽略文件
├── alembic.ini                # 数据库迁移配置
├── pytest.ini                # 测试配置
├── 
├── app/                       # 应用主目录
│   ├── __init__.py
│   ├── main.py               # FastAPI应用入口
│   ├── config.py             # 配置管理
│   ├── dependencies.py       # 依赖注入
│   ├── exceptions.py         # 自定义异常
│   ├── middleware.py         # 中间件
│   ├── 
│   ├── api/                  # API路由层
│   │   ├── __init__.py
│   │   ├── deps.py          # API依赖
│   │   ├── v1/              # API版本1
│   │   │   ├── __init__.py
│   │   │   ├── router.py    # 路由汇总
│   │   │   ├── auth.py      # 认证相关接口
│   │   │   ├── users.py     # 用户管理接口
│   │   │   ├── novels.py    # 小说管理接口
│   │   │   ├── generate.py  # 生成相关接口
│   │   │   └── admin.py     # 管理后台接口
│   │   └── v2/              # API版本2 (预留)
│   │
│   ├── core/                # 核心功能模块
│   │   ├── __init__.py
│   │   ├── auth.py          # 认证核心逻辑
│   │   ├── security.py      # 安全相关工具
│   │   ├── cache.py         # 缓存管理
│   │   ├── database.py      # 数据库连接
│   │   ├── redis_client.py  # Redis客户端
│   │   ├── rate_limiter.py  # 限流器
│   │   └── logger.py        # 日志配置
│   │
│   ├── models/              # 数据模型层
│   │   ├── __init__.py
│   │   ├── base.py          # 基础模型类
│   │   ├── user.py          # 用户模型
│   │   ├── novel.py         # 小说模型
│   │   ├── generation.py    # 生成任务模型
│   │   └── admin.py         # 管理相关模型
│   │
│   ├── schemas/             # Pydantic数据验证模式
│   │   ├── __init__.py
│   │   ├── base.py          # 基础Schema
│   │   ├── user.py          # 用户相关Schema
│   │   ├── novel.py         # 小说相关Schema
│   │   ├── generation.py    # 生成相关Schema
│   │   ├── auth.py          # 认证相关Schema
│   │   └── response.py      # 响应格式Schema
│   │
│   ├── services/            # 业务逻辑层
│   │   ├── __init__.py
│   │   ├── base.py          # 基础服务类
│   │   ├── user_service.py  # 用户服务
│   │   ├── novel_service.py # 小说服务
│   │   ├── generation_service.py # 生成服务
│   │   ├── auth_service.py  # 认证服务
│   │   ├── cache_service.py # 缓存服务
│   │   └── ai_service.py    # AI模型调用服务
│   │
│   ├── repositories/        # 数据访问层
│   │   ├── __init__.py
│   │   ├── base.py          # 基础Repository
│   │   ├── user_repo.py     # 用户数据访问
│   │   ├── novel_repo.py    # 小说数据访问
│   │   ├── generation_repo.py # 生成任务数据访问
│   │   └── cache_repo.py    # 缓存数据访问
│   │
│   ├── tasks/               # 异步任务
│   │   ├── __init__.py
│   │   ├── celery_app.py    # Celery应用配置
│   │   ├── generation_tasks.py # 生成任务
│   │   ├── notification_tasks.py # 通知任务
│   │   └── cleanup_tasks.py # 清理任务
│   │
│   └── utils/               # 工具函数
│       ├── __init__.py
│       ├── common.py        # 通用工具
│       ├── validators.py    # 验证器
│       ├── formatters.py    # 格式化工具
│       ├── encryption.py    # 加密工具
│       └── file_handler.py  # 文件处理工具
│
├── migrations/              # 数据库迁移文件
│   ├── versions/
│   └── env.py
│
├── tests/                   # 测试目录
│   ├── __init__.py
│   ├── conftest.py         # 测试配置
│   ├── test_auth.py        # 认证测试
│   ├── test_users.py       # 用户测试
│   ├── test_novels.py      # 小说测试
│   ├── test_generation.py  # 生成测试
│   └── fixtures/           # 测试数据
│
├── scripts/                # 脚本目录
│   ├── init_db.py         # 数据库初始化
│   ├── create_admin.py    # 创建管理员
│   ├── backup_db.py       # 数据库备份
│   └── deploy.sh          # 部署脚本
│
├── docs/                   # 文档目录
│   ├── api.md             # API文档
│   ├── deployment.md      # 部署文档
│   └── development.md     # 开发文档
│
└── logs/                   # 日志目录
    ├── app.log
    ├── error.log
    └── access.log
```

## 2. 核心模块设计

### 2.1 应用入口 (main.py)
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.v1.router import api_router
from app.core.config import settings
from app.middleware import setup_middleware

def create_application() -> FastAPI:
    app = FastAPI(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        description="AI小说生成Web应用API",
        openapi_url=f"{settings.API_V1_STR}/openapi.json"
    )
    
    # 设置中间件
    setup_middleware(app)
    
    # 注册路由
    app.include_router(api_router, prefix=settings.API_V1_STR)
    
    return app

app = create_application()
```

### 2.2 配置管理 (config.py)
```python
from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 基础配置
    PROJECT_NAME: str = "AI Novel Generator"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # 数据库配置
    DATABASE_URL: str
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # Redis配置
    REDIS_URL: str
    REDIS_PASSWORD: Optional[str] = None
    
    # JWT配置
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # AI服务配置
    AI_SERVICE_URL: str
    AI_SERVICE_API_KEY: str
    
    # 限流配置
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_PER_HOUR: int = 1000
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### 2.3 数据库模型基类 (models/base.py)
```python
from sqlalchemy import Column, Integer, DateTime, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class BaseModel(Base):
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
```

### 2.4 服务基类 (services/base.py)
```python
from typing import TypeVar, Generic, Type, Optional, List
from sqlalchemy.orm import Session
from app.models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)

class BaseService(Generic[ModelType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model
    
    def get(self, db: Session, id: int) -> Optional[ModelType]:
        return db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(self, db: Session, skip: int = 0, limit: int = 100) -> List[ModelType]:
        return db.query(self.model).offset(skip).limit(limit).all()
    
    def create(self, db: Session, obj_in: dict) -> ModelType:
        db_obj = self.model(**obj_in)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update(self, db: Session, db_obj: ModelType, obj_in: dict) -> ModelType:
        for field, value in obj_in.items():
            setattr(db_obj, field, value)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def delete(self, db: Session, id: int) -> ModelType:
        obj = db.query(self.model).get(id)
        db.delete(obj)
        db.commit()
        return obj
```

### 2.5 响应格式标准 (schemas/response.py)
```python
from typing import Any, Optional, Generic, TypeVar
from pydantic import BaseModel

DataType = TypeVar('DataType')

class ResponseModel(BaseModel, Generic[DataType]):
    code: int = 200
    message: str = "success"
    data: Optional[DataType] = None
    timestamp: str
    
    class Config:
        schema_extra = {
            "example": {
                "code": 200,
                "message": "success",
                "data": {},
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }

class PaginatedResponse(BaseModel, Generic[DataType]):
    items: List[DataType]
    total: int
    page: int
    size: int
    pages: int
```

## 3. 开发规范

### 3.1 代码规范
- 使用Python 3.8+语法特性
- 遵循PEP 8代码风格
- 使用类型注解
- 编写文档字符串
- 单元测试覆盖率 > 80%

### 3.2 API设计规范
- RESTful API设计原则
- 统一的错误处理
- 请求参数验证
- 响应格式标准化
- API版本管理

### 3.3 数据库规范
- 使用SQLAlchemy ORM
- 数据库迁移管理
- 索引优化
- 外键约束
- 软删除机制

### 3.4 缓存规范
- 缓存键命名规范
- TTL设置策略
- 缓存更新策略
- 缓存穿透防护
- 缓存一致性保证

## 4. 部署配置

### 4.1 环境变量配置
```bash
# .env 文件示例
PROJECT_NAME="AI Novel Generator"
VERSION="1.0.0"

# 数据库配置
DATABASE_URL="mysql+pymysql://user:password@localhost:3306/bookweb"
DATABASE_POOL_SIZE=10

# Redis配置
REDIS_URL="redis://localhost:6379/0"
REDIS_PASSWORD=""

# JWT配置
SECRET_KEY="your-secret-key-here"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI服务配置
AI_SERVICE_URL="https://api.openai.com/v1"
AI_SERVICE_API_KEY="your-api-key-here"
```

### 4.2 Docker配置
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 4.3 依赖管理
```txt
# requirements.txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
pymysql==1.1.0
redis==5.0.1
celery==5.3.4
pydantic==2.5.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
alembic==1.13.0
pytest==7.4.3
pytest-asyncio==0.21.1
```

这个项目结构设计提供了：
1. 清晰的模块分层架构
2. 标准化的代码组织方式
3. 完整的配置管理机制
4. 统一的开发规范
5. 便于维护和扩展的设计模式
