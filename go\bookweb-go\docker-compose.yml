version: '3.8'

services:
  # API服务
  api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - APP_MODE=production
      - DATABASE_MYSQL_HOST=mysql
      - DATABASE_MYSQL_PORT=3306
      - DATABASE_MYSQL_USERNAME=bookweb
      - DATABASE_MYSQL_PASSWORD=password123
      - DATABASE_MYSQL_DATABASE=bookweb
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    networks:
      - bookweb-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: bookweb
      MYSQL_USER: bookweb
      MYSQL_PASSWORD: password123
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    restart: unless-stopped
    networks:
      - bookweb-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - bookweb-network

  # Nginx负载均衡（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - bookweb-network

volumes:
  mysql_data:
  redis_data:

networks:
  bookweb-network:
    driver: bridge
