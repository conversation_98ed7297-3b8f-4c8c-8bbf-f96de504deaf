package model

import (
	"time"
	"gorm.io/gorm"
)

type Novel struct {
	ID                uint           `json:"id" gorm:"primaryKey"`
	UserID            uint           `json:"user_id" gorm:"not null;index"`
	Title             string         `json:"title" gorm:"size:200;not null"`
	Description       string         `json:"description" gorm:"type:text"`
	Genre             string         `json:"genre" gorm:"size:50"`
	Status            int            `json:"status" gorm:"default:0"` // 0:创建中 1:完成 2:暂停
	TotalChapters     int            `json:"total_chapters" gorm:"default:0"`
	CompletedChapters int            `json:"completed_chapters" gorm:"default:0"`
	TotalWords        int            `json:"total_words" gorm:"default:0"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User     User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Chapters []Chapter `json:"chapters,omitempty" gorm:"foreignKey:NovelID"`
}

func (Novel) TableName() string {
	return "novels"
}
