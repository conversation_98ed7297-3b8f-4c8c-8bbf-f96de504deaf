# BookWeb Go - 高性能AI小说生成API

基于Golang的高性能AI小说生成系统，支持50+用户同时在线，集成LangChain Go实现智能重试和故障转移。

## 🚀 特性

- **高并发**: 基于Goroutines，支持数万并发连接
- **AI集成**: 集成LangChain Go，支持多AI提供商
- **实时通信**: WebSocket实时推送生成进度
- **智能重试**: 指数退避重试机制，自动故障转移
- **缓存优化**: Redis多级缓存策略
- **监控完善**: Prometheus指标 + 结构化日志
- **容器化**: Docker + Docker Compose一键部署

## 📁 项目结构

```
bookweb-go/
├── cmd/                    # 应用入口
│   ├── api/               # API服务
│   ├── worker/            # 异步任务处理器
│   └── migrate/           # 数据库迁移工具
├── internal/              # 内部包
│   ├── api/               # API层
│   ├── service/           # 业务逻辑层
│   ├── repository/        # 数据访问层
│   ├── model/             # 数据模型
│   ├── config/            # 配置管理
│   └── ...
├── pkg/                   # 公共包
├── configs/               # 配置文件
├── migrations/            # 数据库迁移
└── docker/                # Docker配置
```

## 🛠️ 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 7.0+

### 安装依赖

```bash
# 下载依赖
make deps

# 或者手动执行
go mod download
go mod tidy
```

### 配置

1. 复制配置文件：
```bash
cp configs/config.yaml configs/config.local.yaml
```

2. 修改配置文件中的数据库连接信息和AI API密钥

### 运行

```bash
# 开发模式运行
make run

# 或者使用热重载（需要安装air）
make install-air
make dev
```

### 构建

```bash
# 构建应用
make build

# Linux构建
make build-linux

# Docker构建
make docker-build
```

## 🧪 测试

```bash
# 运行测试
make test

# 测试覆盖率
make test-coverage
```

## 📊 API文档

### 健康检查
```
GET /health
```

### 用户相关
```
POST /api/v1/users/register  # 用户注册
POST /api/v1/users/login     # 用户登录
```

### 小说管理
```
GET  /api/v1/novels          # 获取小说列表
POST /api/v1/novels          # 创建小说
```

### 章节管理
```
GET  /api/v1/chapters        # 获取章节列表
POST /api/v1/chapters        # 创建章节
GET  /api/v1/chapters/:id    # 获取章节详情
```

### WebSocket
```
WS /ws                       # WebSocket连接
```

## 🐳 Docker部署

```bash
# 启动所有服务
make docker-run

# 停止服务
make docker-stop
```

## 📈 性能指标

- **并发用户**: 50+用户同时在线
- **响应时间**: API响应<100ms
- **吞吐量**: 单机QPS 5000+
- **内存占用**: 运行时<512MB
- **AI生成**: 平均60-120秒

## 🔧 开发工具

```bash
# 代码格式化
make fmt

# 代码检查（需要安装golangci-lint）
make install-lint
make lint

# 数据库迁移
make migrate-up
make migrate-down
```

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
