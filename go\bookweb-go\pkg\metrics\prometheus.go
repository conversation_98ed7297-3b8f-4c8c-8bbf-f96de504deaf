package metrics

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

var (
	// HTTP请求总数
	httpRequestsTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "endpoint", "status"},
	)

	// HTTP请求持续时间
	httpRequestDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "HTTP request duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)

	// AI生成任务指标
	aiGenerationTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "ai_generation_total",
			Help: "Total number of AI generation tasks",
		},
		[]string{"status", "endpoint"},
	)

	aiGenerationDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "ai_generation_duration_seconds",
			Help:    "AI generation duration in seconds",
			Buckets: []float64{1, 5, 10, 30, 60, 120, 300},
		},
		[]string{"endpoint"},
	)

	// 数据库连接池指标
	dbConnectionsInUse = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "db_connections_in_use",
			Help: "Number of database connections currently in use",
		},
	)

	dbConnectionsIdle = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "db_connections_idle",
			Help: "Number of idle database connections",
		},
	)
)

func init() {
	prometheus.MustRegister(
		httpRequestsTotal,
		httpRequestDuration,
		aiGenerationTotal,
		aiGenerationDuration,
		dbConnectionsInUse,
		dbConnectionsIdle,
	)
}

// PrometheusMiddleware Gin中间件
func PrometheusMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		c.Next()

		duration := time.Since(start).Seconds()
		status := strconv.Itoa(c.Writer.Status())

		httpRequestsTotal.WithLabelValues(c.Request.Method, c.FullPath(), status).Inc()
		httpRequestDuration.WithLabelValues(c.Request.Method, c.FullPath()).Observe(duration)
	}
}

// MetricsHandler 暴露指标端点
func MetricsHandler() gin.HandlerFunc {
	h := promhttp.Handler()
	return func(c *gin.Context) {
		h.ServeHTTP(c.Writer, c.Request)
	}
}

// RecordAIGeneration 记录AI生成指标
func RecordAIGeneration(endpoint, status string, duration time.Duration) {
	aiGenerationTotal.WithLabelValues(status, endpoint).Inc()
	aiGenerationDuration.WithLabelValues(endpoint).Observe(duration.Seconds())
}

// UpdateDBConnectionMetrics 更新数据库连接指标
func UpdateDBConnectionMetrics(inUse, idle int) {
	dbConnectionsInUse.Set(float64(inUse))
	dbConnectionsIdle.Set(float64(idle))
}
