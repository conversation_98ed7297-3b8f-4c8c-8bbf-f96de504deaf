package request

type CreateChapterRequest struct {
	NovelID     uint                   `json:"novel_id" binding:"required"`
	Title       string                 `json:"title" binding:"required,max=200"`
	Prompt      string                 `json:"prompt" binding:"required,max=2000"`
	ChapterInfo map[string]interface{} `json:"chapter_info"`
}

type UpdateChapterRequest struct {
	Title   string `json:"title" binding:"max=200"`
	Content string `json:"content"`
}

type ListChaptersRequest struct {
	NovelID uint `form:"novel_id" binding:"required"`
	Page    int  `form:"page,default=1" binding:"min=1"`
	Size    int  `form:"size,default=20" binding:"min=1,max=100"`
}
